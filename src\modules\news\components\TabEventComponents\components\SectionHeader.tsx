import React, {FC, useEffect} from 'react';
import {StyleSheet, Text, View, TouchableOpacity} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {useDispatch, useSelector} from 'react-redux';
import {RootState} from '../../../../../redux/store/store';
import {NewsEventActions} from '../../../../../redux/actions/newEventAction';

interface SectionHeaderProps {
  title: string;
  showSeeMore?: boolean;
}

export const SectionHeader: FC<SectionHeaderProps> = ({
  title,
  showSeeMore = false,
}) => {
  const dispatch = useDispatch();
  const getData = async () => {
    await NewsEventActions.getData(dispatch);
  };
  useEffect(() => {
    getData();
  }, []);
  return (
    <View style={styles.sectionHeader}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {showSeeMore && (
        <TouchableOpacity>
          <Text style={styles.seeMore}>Xem thêm</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginTop: 24,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ColorThemes.light.infor_text_color,
  },
  seeMore: {
    fontSize: 14,
    color: ColorThemes.light.infor_text_color,
    fontWeight: '600',
  },
});
