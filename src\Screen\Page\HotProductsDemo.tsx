import React from 'react';
import {View, StyleSheet, ScrollView, Alert} from 'react-native';
import HotProductsRow from '../../modules/Product/HotProductsRow';
import {SquareProductItem} from '../../components/SquareProductCard';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../router/router';

// Dữ liệu mẫu cho sản phẩm HOT
const hotProducts: SquareProductItem[] = [
  {
    Id: '1',
    Name: 'B<PERSON> sản phẩm Quần + áo mùa hè',
    Price: 99000,
    Img: 'https://placehold.co/200x200/FFC0CB/ffffff?text=Fashion+1',
    rating: 4.5,
    soldCount: 200,
  },
  {
    Id: '2',
    Name: 'Bộ sản phẩm Quần + áo mùa hè',
    Price: 99000,
    Img: 'https://placehold.co/200x200/FFC0CB/ffffff?text=Fashion+2',
    rating: 4.5,
    soldCount: 200,
  },
  {
    Id: '3',
    Name: '<PERSON><PERSON> sản phẩm Quần + áo mùa hè',
    Price: 99000,
    Img: 'https://placehold.co/200x200/FFC0CB/ffffff?text=Fashion+3',
    rating: 4.5,
    soldCount: 200,
  },
  {
    Id: '4',
    Name: 'Bộ sản phẩm Quần + áo mùa hè',
    Price: 99000,
    Img: 'https://placehold.co/200x200/FFC0CB/ffffff?text=Fashion+4',
    rating: 4.5,
    soldCount: 200,
  },
];

// Dữ liệu mẫu cho sản phẩm mới
const newProducts: SquareProductItem[] = [
  {
    Id: '5',
    Name: 'Áo thun nữ thời trang',
    Price: 79000,
    Img: 'https://placehold.co/200x200/87CEEB/ffffff?text=Fashion+5',
    rating: 4.2,
    soldCount: 150,
  },
  {
    Id: '6',
    Name: 'Quần jean nam cao cấp',
    Price: 199000,
    Img: 'https://placehold.co/200x200/87CEEB/ffffff?text=Fashion+6',
    rating: 4.7,
    soldCount: 320,
  },
  {
    Id: '7',
    Name: 'Váy đầm dự tiệc',
    Price: 299000,
    Img: 'https://placehold.co/200x200/87CEEB/ffffff?text=Fashion+7',
    rating: 4.8,
    soldCount: 180,
  },
  {
    Id: '8',
    Name: 'Áo khoác denim unisex',
    Price: 359000,
    Img: 'https://placehold.co/200x200/87CEEB/ffffff?text=Fashion+8',
    rating: 4.6,
    soldCount: 210,
  },
];

const HotProductsDemo = () => {
  const navigation = useNavigation<any>();

  // Xử lý sự kiện khi nhấn vào sản phẩm
  const handleProductPress = (product: SquareProductItem) => {
    // Chuyển đến trang chi tiết sản phẩm
    navigation.navigate(RootScreen.ProductDetail, {id: product.Id});
  };

  // Xử lý sự kiện khi nhấn vào nút "Xem thêm"
  const handleSeeAll = (category: string) => {
    Alert.alert('Thông báo', `Xem tất cả sản phẩm trong danh mục: ${category}`);
  };

  return (
    <ScrollView style={styles.container}>
      {/* Hiển thị sản phẩm HOT */}
      <HotProductsRow
        title="Sản phẩm HOT"
        products={hotProducts}
        onSeeAll={() => handleSeeAll('Sản phẩm HOT')}
        onProductPress={handleProductPress}
      />

      {/* Hiển thị sản phẩm mới */}
      <HotProductsRow
        title="Sản phẩm mới"
        products={newProducts}
        onSeeAll={() => handleSeeAll('Sản phẩm mới')}
        onProductPress={handleProductPress}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});

export default HotProductsDemo;
