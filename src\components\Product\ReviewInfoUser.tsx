import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
  Image,
  TextInput,
} from 'react-native';
import {Rating} from 'wini-mobile-components';

export const ReviewInfoUser = ({item, index}: {item: any; index: number}) => {
  return (
    <View style={{flexDirection: 'column', alignItems: 'center'}}>
      <View>
        <Image
          source={{
            uri: item.avatar,
          }}
          style={styles.avatar}
        />
        <View>
          <Text style={styles.name}>{item.UserName}</Text>
          <Text style={styles.rating}>
            <Rating value={item?.rating} size={20} />
          </Text>
        </View>
      </View>
      <View>
        <View style={styles.WrapperInput}>
          <TextInput
            style={styles.input}
            placeholder="Nhập nội dung đánh giá"
            placeholderTextColor="#999"
          />
          <Text style={styles.errorText}>Bạn chưa đánh giá sản phẩm này</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  avatar: {
    borderWidth: 5,
    borderRadius: 50,
    width: 50,
    height: 50,
    marginRight: 10,
    borderColor: '#F8F8FF',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  name: {
    fontWeight: 'bold',
  },
  rating: {
    color: '#FFD700', // Màu vàng cho sao
  },
  WrapperInput: {
    width: 300,
    marginVertical: 10,
  },
  input: {
    borderWidth: 2,
    borderColor: '#ff0000',
    borderRadius: 5,
    padding: 10,
    fontSize: 16,
    color: '#000',
  },
  errorText: {
    color: '#ff0000',
    fontSize: 14,
    marginTop: 5,
  },
});
