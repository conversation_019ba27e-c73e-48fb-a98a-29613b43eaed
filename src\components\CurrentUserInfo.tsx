import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../assets/skin/colors';
import { TypoSkin } from '../assets/skin/typography';

interface CurrentUserInfoProps {
  userName: string;
  currentPoints: number;
}

const CurrentUserInfo: React.FC<CurrentUserInfoProps> = ({
  userName,
  currentPoints,
}) => {
  const formatNumber = (num: number) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Winicon 
            src="fill/users/profile" 
            size={24} 
            color={ColorThemes.light.white} 
          />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.label}>Ngư<PERSON>i gửi</Text>
          <Text style={styles.userName}>{userName}</Text>
        </View>
        <View style={styles.pointsContainer}>
          <Text style={styles.pointsLabel}>CPoint</Text>
          <Text style={styles.pointsValue}>{formatNumber(currentPoints)}</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorThemes.light.secondary2_background,
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ColorThemes.light.secondary2_main_color,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContainer: {
    flex: 1,
  },
  label: {
    ...TypoSkin.label3,
    color: ColorThemes.light.secondary2_main_color,
    marginBottom: 4,
  },
  userName: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '600',
  },
  pointsContainer: {
    alignItems: 'flex-end',
  },
  pointsLabel: {
    ...TypoSkin.label4,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 2,
  },
  pointsValue: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.secondary2_main_color,
    fontWeight: 'bold',
  },
});

export default CurrentUserInfo;
