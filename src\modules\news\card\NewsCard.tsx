import React from 'react';
import {View, Text, StyleSheet, Image, TouchableOpacity} from 'react-native';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
  faThumbsUp,
  faComment,
  faBookmark,
  faShare,
} from '@fortawesome/free-solid-svg-icons';
import {NewsItem} from '../../../redux/models/news';
import {formatTimeAgo} from '../../../utils/Utils';

const NewsCard = ({item}: {item: NewsItem}) => {
  const {Name, Description, relativeUser, Likes, Img, Comment, CreatedAt} =
    item;

  return (
    <View style={styles.cardContainer}>
      <View style={styles.contentContainer}>
        <View style={styles.textContainer}>
          <View style={styles.header}>
            <Image source={{uri: relativeUser.image}} style={styles.avatar} />
            <View style={styles.authorInfo}>
              <Text style={styles.authorName}>{relativeUser.title}</Text>
              <Text style={styles.timestamp}>{formatTimeAgo(CreatedAt)}</Text>
            </View>
          </View>
          <Text style={styles.title} numberOfLines={2}>
            {Name}
          </Text>
          <Text style={styles.description} numberOfLines={2}>
            {Description}
          </Text>

          {/* Footer */}
          <View style={styles.footer}>
            <View style={styles.statsContainer}>
              <TouchableOpacity style={styles.statItem}>
                <FontAwesomeIcon icon={faThumbsUp} size={16} color="#555" />
                <Text style={styles.statText}>{Likes}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.statItem}>
                <FontAwesomeIcon icon={faComment} size={16} color="#555" />
                <Text style={styles.statText}>{Comment}</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.actionsContainer}>
              <TouchableOpacity>
                <FontAwesomeIcon
                  icon={faBookmark}
                  size={16}
                  color="#555"
                  style={styles.actionIcon}
                />
              </TouchableOpacity>
              <TouchableOpacity>
                <FontAwesomeIcon
                  icon={faShare}
                  size={16}
                  color="#555"
                  style={styles.actionIcon}
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
        <Image source={{uri: Img}} style={styles.thumbnail} />
      </View>
    </View>
  );
};

// Styles không thay đổi
const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 8,
    marginHorizontal: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 20,
    height: 20,
    borderRadius: 20,
  },
  authorInfo: {
    marginLeft: 8,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  authorName: {
    fontSize: 10,
    color: '#333',
  },
  timestamp: {
    fontSize: 10,
    color: '#888',
    marginLeft: 4,
  },
  contentContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  textContainer: {
    flex: 1,
    marginRight: 16,
  },
  title: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#111',
    marginBottom: 8,
    lineHeight: 24,
  },
  description: {
    fontSize: 12,
    color: '#666',
    lineHeight: 20,
  },
  thumbnail: {
    width: 130,
    height: '100%',
    borderRadius: 8,
    backgroundColor: '#eee',
  },
  footer: {
    marginTop: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 14,
  },
  statText: {
    marginLeft: 8,
    fontSize: 12,
    color: '#555',
  },
  actionsContainer: {
    flexDirection: 'row',
  },
  actionIcon: {
    marginLeft: 20,
  },
});

export default NewsCard;
