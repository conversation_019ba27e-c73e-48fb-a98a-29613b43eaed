import {
  Dispatch,
  PayloadAction,
  UnknownAction,
  createSlice,
} from '@reduxjs/toolkit';

import {NotificationItem} from '../models/notification';

const data: NotificationItem[] = [
  // Hôm nay
  {
    Id: '1',
    Name: '<PERSON>ia<PERSON> dịch đổi quà',
    DateCreated: new Date('2025-06-11T17:40:00.000Z').getTime(),
    Sort: 1,
    Content: 'Giao dịch đổi quà của bạn vừa được phê duy<PERSON>, bạn bị trừ (-900$)',
    Status: 0, // unread
    Type: 1,
    CustomerId: '1',
    ConfigNotificationId: '1',
  },
  {
    Id: '2',
    Name: 'Nhận hoa hồng',
    DateCreated: new Date('2025-06-11T17:30:00.000Z').getTime(),
    Sort: 2,
    Content:
      'Bạn vừa nhận được 200$ hoa hồng từ giao dịch mua hàng của "<PERSON>uy<PERSON><PERSON>h Tùng"',
    Status: 1, // read
    Type: 2,
    CustomerId: '1',
    ConfigNotificationId: '2',
  },
  // Ngày hôm trước
  {
    Id: '3',
    Name: 'Nhận hoa hồng',
    DateCreated: new Date('2025-06-10T17:40:00.000Z').getTime(),
    Sort: 3,
    Content:
      'Bạn vừa nhận được 200$ hoa hồng từ giao dịch mua hàng của "Nguyễn Thanh Tùng"',
    Status: 0, // unread
    Type: 3,
    CustomerId: '1',
    ConfigNotificationId: '3',
  },
  {
    Id: '4',
    Name: 'Giao dịch đổi quà',
    DateCreated: new Date('2025-06-10T17:40:00.000Z').getTime(),
    Sort: 4,
    Content: 'Giao dịch đổi quà của bạn vừa được phê duyệt, bạn bị trừ (-900$)',
    Status: 1, // read
    Type: 1,
    CustomerId: '1',
    ConfigNotificationId: '4',
  },
];

export enum NotificationType {
  invite = 1,
  inviteConfirm = 2,
  assignTask = 3,
  changeTaskStatus = 4,
  newToiletServices = 5,
  accpetToiletServices = 6,
  changeToiletStatus = 7,
  changeToiletServicesStatus = 8,
  registerPartner = 9,
  registerGreenToilet = 10,
}

interface notificationSimpleResponse {
  data: Array<NotificationItem>;
  badge: number;
  onLoading?: boolean;
  type?: string;
}

export type {notificationSimpleResponse};

const initState: notificationSimpleResponse = {
  data: [],
  badge: 0,
};

export const notificationSlice = createSlice({
  name: 'notification',
  initialState: initState,
  reducers: {
    setData: <K extends keyof notificationSimpleResponse>(
      state: notificationSimpleResponse,
      action: PayloadAction<{
        stateName: K;
        data: notificationSimpleResponse[K];
      }>,
    ) => {
      state[action.payload.stateName] = action.payload.data;
    },
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case 'GETDATA':
          state.data = action.payload.data;
          state.badge = action.payload.totalCount;
          break;
        case 'GETMORE':
          state.data = [...state.data, ...action.payload.data];
          state.badge = action.payload.totalCount;
          break;
        case 'ADD':
          state.data = [...state.data, ...action.payload.data];
          break;
        case 'SETBADGE':
          state.badge = action.payload.badge;
          break;
        case 'EDIT':
          state.data = state.data.map(e => {
            const _tmp = action.payload.data.find(
              (el: NotificationItem) => el.Id === e.Id,
            );
            return _tmp ?? e;
          });
          break;
        case 'DELETE':
          state.data = state.data.filter(e =>
            action.payload.data.every((id: string) => e.Id !== id),
          );
          break;
        default:
          break;
      }
      state.onLoading = false;
    },
    onFetching: state => {
      state.data = [];
      state.onLoading = true;
    },
    onResetNotification: state => {
      state.data = [];
      state.badge = 0;
      state.onLoading = true;
    },
  },
});

export const {setData, handleActions, onFetching, onResetNotification} =
  notificationSlice.actions;

export default notificationSlice.reducer;
