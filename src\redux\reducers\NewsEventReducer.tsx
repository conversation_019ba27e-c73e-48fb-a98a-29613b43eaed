import {PayloadAction, createSlice} from '@reduxjs/toolkit';
import {NewsEvent} from '../models/newsEvent';
import {fetchNewsEvents} from '../actions/newEventAction';

interface EventStoreState {
  data: Array<NewsEvent>;
  badge: number;
  loading?: boolean;
  type?: string;
}

export type {EventStoreState};

const initState: EventStoreState = {
  data: [],
  badge: 0,
  loading: false,
};

export const newsEventSlice = createSlice({
  name: 'newsEvent',
  initialState: initState,
  reducers: {
    setData: <K extends keyof EventStoreState>(
      state: EventStoreState,
      action: PayloadAction<{
        stateName: K;
        data: EventStoreState[K];
      }>,
    ) => {
      state[action.payload.stateName] = action.payload.data;
    },
    onFetching: (state: EventStoreState, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    onFetchDone: (state: EventStoreState) => {
      state.loading = false;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchNewsEvents.pending, state => {
        state.loading = true;
      })
      .addCase(
        fetchNewsEvents.fulfilled,
        (state, action: PayloadAction<NewsEvent[]>) => {
          state.loading = false;
          state.data = action.payload;
        },
      )
      .addCase(fetchNewsEvents.rejected, state => {
        state.loading = false;
      });
  },
});

export const {setData, onFetching, onFetchDone} = newsEventSlice.actions;

export default newsEventSlice.reducer;
