import React from 'react';
import { TouchableOpacity, StyleSheet, View, Text } from 'react-native';
import { Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../assets/skin/colors';
import { TypoSkin } from '../assets/skin/typography';
import { navigate, RootScreen } from '../router/router';
import { useCartState } from '../redux/hook/cartHook';

interface CartButtonProps {
  size?: number;
  color?: string;
  showBadge?: boolean;
  style?: any;
}

const CartButton: React.FC<CartButtonProps> = ({
  size = 24,
  color = ColorThemes.light.neutral_text_title_color,
  showBadge = true,
  style,
}) => {
  // Lấy state của giỏ hàng
  const cartState = useCartState();
  
  // Tính tổng số lượng sản phẩm trong giỏ hàng
  const itemCount = cartState.items.length;
  
  return (
    <TouchableOpacity 
      style={[styles.container, style]} 
      onPress={() => navigate(RootScreen.CartPage)}
      activeOpacity={0.7}
    >
      <Winicon 
        src="outline/shopping/cart" 
        size={size} 
        color={color} 
      />
      
      {showBadge && itemCount > 0 && (
        <View style={styles.badge}>
          <Text style={styles.badgeText}>
            {itemCount > 99 ? '99+' : itemCount}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    padding: 8,
  },
  badge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: ColorThemes.light.secondary1_main_color,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 10,
    fontWeight: 'bold',
  },
});

export default CartButton;
