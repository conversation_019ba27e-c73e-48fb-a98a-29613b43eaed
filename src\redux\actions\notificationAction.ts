import {UnknownAction} from '@reduxjs/toolkit';
import {Dispatch} from 'react';
import {showSnackbar, ComponentStatus} from 'wini-mobile-components';
import store from '../store/store';
import notifee from '@notifee/react-native';
import {DataController} from '../../base/baseController';
import {
  handleActions,
  onFetching,
  onResetNotification,
} from '../reducers/notificationReducer';
import {NotificationItem} from '../models/notification';

export class NotificationActions {
  static setBadge = (dispatch: Dispatch<UnknownAction>) => {
    // dispatch(onFetching())
    notifee.getBadgeCount().then(count =>
      dispatch(
        handleActions({
          type: 'SETBADGE',
          badge: count,
        }),
      ),
    );
  };

  static getData = async (
    dispatch: Dispatch<UnknownAction>,
    config?: {page?: number; status?: number},
  ) => {
    dispatch(onFetching());
    const controller = new DataController('Notification');
    const cusId = store.getState().customer.data?.Id;
    const res = await controller.aggregateList({
      page: config?.page ?? 1,
      size: 1000,
      searchRaw: `@CustomerId:{${cusId}}`,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res.code !== 200)
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    NotificationActions.setBadge(dispatch);
    dispatch(
      handleActions({
        type: config?.page && config.page > 1 ? 'GETMORE' : 'GETDATA',
        data: res.data,
        totalCount: res.totalCount,
      }),
    );
  };

  static add = async (
    dispatch: Dispatch<UnknownAction>,
    data: Array<NotificationItem>,
  ) => {
    const controller = new DataController('Notification');
    const res = await controller.add(data);
    if (res.code !== 200)
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    dispatch(
      handleActions({
        type: 'ADD',
        data: data,
      }),
    );
  };

  static edit = async (
    dispatch: Dispatch<UnknownAction>,
    data: Array<NotificationItem>,
  ) => {
    const controller = new DataController('Notification');
    const res = await controller.edit(data);
    if (res.code !== 200)
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    dispatch(
      handleActions({
        type: 'EDIT',
        data: data,
      }),
    );
    return res;
  };

  static delete = async (
    dispatch: Dispatch<UnknownAction>,
    data: Array<string>,
  ) => {
    const controller = new DataController('Notification');
    const res = await controller.delete(data);
    if (res.code !== 200)
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    dispatch(
      handleActions({
        type: 'DELETE',
        data: data,
      }),
    );
  };

  static reset = (dispatch: Dispatch<UnknownAction>) => {
    dispatch(onResetNotification());
  };
}
