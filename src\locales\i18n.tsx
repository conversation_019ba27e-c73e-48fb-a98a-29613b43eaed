import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import * as RNLocalize from 'react-native-localize';
import AsyncStorage from '@react-native-async-storage/async-storage';

import en from './en.json';
import vi from './vi.json';
import ja from './ja.json';

// L<PERSON>y ngôn ngữ mặc định từ hệ thống
const getDeviceLanguage = () => {
  const locales = RNLocalize.getLocales();
  return locales[0]?.languageCode || 'en';
};

const loadLanguage = async () => {
  const storedLang = await AsyncStorage.getItem('language');
  return storedLang || getDeviceLanguage();
};

loadLanguage().then((language) => {
  i18n.use(initReactI18next).init({
    resources: { en: { translation: en }, vi: { translation: vi }, ja: {translation: ja} },
    lng: language,
    fallbackLng: 'en',
    interpolation: { escapeValue: false },
  });
});

export default i18n;
