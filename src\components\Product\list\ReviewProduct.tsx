import React, { use, useEffect, useState } from 'react';
import { View, Image, Text, TouchableOpacity, StyleSheet, Pressable } from 'react-native';
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import { Winicon } from 'wini-mobile-components';
import { TypeMenuReview } from '../../../Config/Contanst';
import { ReviewData, ReviewOrderData } from '../../../mock/shopData';
import { ReviewDataDto, ReviewProductProps } from '../../dto/dto';
import ReviewProductIteCard from '../card/CardReviewItem';
import { DataController } from '../../../base/baseController';
import { useSelectorShopState } from '../../../redux/hook/shopHook ';


const ReviewProduct = (props: ReviewProductProps) => {
    const { type } = props
    const [data, setData] = useState<ReviewDataDto[] | any[]>([])
    const ProductController = new DataController('Product');
    const RatingController = new DataController('Rating');
    const shopInfo = useSelectorShopState().data;
    useEffect(() => {
        if (type == TypeMenuReview.Product) {
            setData(ReviewData)
        } else {
            setData(ReviewOrderData)
        }
    }, [type])
    let callApi = async () => {
        let res = await ProductController.aggregateList({
            searchRaw: `@ShopId:{${shopInfo[0]?.Id}}`,
        })
        if (res?.code === 200) {
            let arrayProduct: string[] = [];
            res.data.forEach((item: any) => {
                arrayProduct.push(item.Id)
            })
            console.log("check-arrayProduct", arrayProduct)
            let resRating = await RatingController.aggregateList({
                searchRaw: `@ProductId: ${arrayProduct}`,
            })
            console.log("check-resRating", resRating)
        }
    }
    useEffect(() => {
        callApi()
    }, [])

    return (
        <FlatList
            data={data}
            style={{ flex: 1 }}
            keyExtractor={(item, i) => `${i} ${item.Id}`}
            renderItem={({ item, index }) => ReviewProductIteCard({ item, index }, type as string)}
        />
    );
};

export default ReviewProduct;