import React, {forwardRef, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  Dimensions,
  SafeAreaView,
  ScrollView,
  KeyboardAvoidingView,
  TouchableOpacity,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {
  AppButton,
  closePopup,
  ComponentStatus,
  FBottomSheet,
  FDialog,
  FPopup,
  NumberPicker,
  showBottomSheet,
  showDialog,
  showPopup,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import {DefaultProduct, SkeletonPlaceCard} from '../card/defaultProduct';
import {DefaultProductQuantity} from '../card/defaultProductQuantity';
import ScreenHeader from '../../../Screen/Layout/header';
import DefaultLoadmore from './defaultLoadMore';
import WScreenFooter from '../../../Screen/Layout/footer';
import {useForm} from 'react-hook-form';
import {TextFieldForm} from '../form/component-form';

interface Props {
  horizontal?: boolean;
  titleList?: string;
  isSeeMore?: boolean;
}

export default function ByRelativeCount(props: Props) {
  // const [data, setData] = useState<Array<any>>([]);

  const [isLoading, setLoading] = useState(false);
  const [isRefresh, setRefresh] = useState(false);
  const [isLoadMore, setLoadMore] = useState(false);

  const bottomSheetRef = useRef<any>(null); // refBBVottomSheet
  const dialogRef = useRef<any>(null); // refBBVottomSheet

  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {products: []},
  });

  useEffect(() => {
    onData();
  }, []);

  const onData = () => {
    setLoading(true);
    methods.setValue('products', []);
    setTimeout(() => {
      methods.setValue('products', [
        {
          Id: '1',
          Name: 'Item 1',
          Img: null,
          Price: 100,
          Quantity: 1,
        },
      ]);
      setLoading(false);
    }, 2000);
  };

  const onRefresh = () => {
    setRefresh(true);
    methods.setValue('products', []);

    setTimeout(() => {
      methods.setValue('products', [
        {
          Id: '1',
          Name: 'Item 1',
          Img: null,
          Price: 100,
        },
      ]);
      setRefresh(false);
    }, 2000);
  };

  return (
    <View style={{width: '100%', height: props.horizontal ? 386 : undefined}}>
      <FPopup ref={bottomSheetRef} />
      <FDialog ref={dialogRef} />
      {props.titleList ? (
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
            paddingBottom: 16,
            flexDirection: 'row',
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
          }}>
          <Text
            style={{
              ...TypoSkin.heading5,
              color: ColorThemes.light.neutral_text_title_color,
            }}>
            {props.titleList}
          </Text>
          {props.isSeeMore ? (
            <AppButton
              title={'Add more'}
              containerStyle={{
                justifyContent: 'flex-start',
              }}
              backgroundColor={'transparent'}
              textStyle={TypoSkin.buttonText3}
              borderColor="transparent"
              suffixIconSize={16}
              suffixIcon={'outline/arrows/circle-arrow-right'}
              onPress={() => {}}
              textColor={ColorThemes.light.infor_main_color}
            />
          ) : null}
        </View>
      ) : null}
      <FlatList
        data={methods.watch('products')}
        nestedScrollEnabled
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefresh}
            onRefresh={() => {
              onRefresh();
            }}
          />
        }
        ListHeaderComponent={() => {
          return (
            <AppButton
              title={'Add more'}
              containerStyle={{
                justifyContent: 'flex-start',
                alignSelf: 'baseline',
                marginVertical: 8,
              }}
              backgroundColor={'transparent'}
              textStyle={TypoSkin.buttonText3}
              borderColor="transparent"
              onPress={() => {
                showPopup({
                  ref: bottomSheetRef,
                  enableDismiss: true,
                  children: (
                    <PopupView methods={methods} ref={bottomSheetRef} />
                  ),
                });
              }}
              textColor={ColorThemes.light.infor_main_color}
            />
          );
        }}
        contentContainerStyle={{
          gap: 16,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}
        renderItem={({item, index}) => {
          return (
            <DefaultProductQuantity
              flexDirection="row"
              mainContainerStyle={{
                alignItems: 'flex-start',
              }}
              containerStyle={{
                paddingTop: 16,
              }}
              data={item}
              onPressQuantityAction={value => {
                if (value == 0) {
                  showDialog({
                    ref: dialogRef,
                    title: 'Chắc chắn xóa sản phẩm này ?',
                    onSubmit: async () => {
                      methods.setValue(
                        'products',
                        methods
                          .watch('products')
                          .filter((product: any) => product.Id !== item.Id),
                      );
                    },
                  });
                  return;
                }
                methods.setValue(
                  'products',
                  methods.watch('products').map((item: any) => {
                    if (item.Id === item.Id) {
                      // This is always true - should be comparing with the outer item
                      return {
                        ...item,
                        Quantity: value,
                      };
                    }
                    return item;
                  }),
                );
              }}
              onPressDeleteAction={() => {
                showDialog({
                  ref: dialogRef,
                  title: 'Chắc chắn xóa sản phẩm này ?',
                  onSubmit: async () => {
                    methods.setValue(
                      'products',
                      methods
                        .watch('products')
                        .filter((product: any) => product.Id !== item.Id),
                    );
                  },
                });
              }}
            />
          );
        }}
        style={{width: '100%', height: '100%', paddingHorizontal: 16}}
        keyExtractor={item => item.Id.toString()}
        horizontal={props.horizontal}
        ListFooterComponent={() => {
          return (
            <View style={{}}>
              <Text style={{...TypoSkin.body1}}>
                Tổng số:{' '}
                {methods
                  .watch('products')
                  .map((item: any) => item.Price * item.Quantity)
                  .reduce((a: any, b: any) => a + b, 0) ?? 0}
              </Text>
            </View>
          );
        }}
        ListEmptyComponent={() => {
          if (isLoading) {
            return <SkeletonPlaceCard />;
          }
          if (isLoadMore) {
            return (
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  flexDirection: 'row',
                }}>
                <ActivityIndicator
                  color={ColorThemes.light.primary_main_color}
                />
              </View>
            );
          }
          return <Text style={{color: '#000000'}}>Không có dữ liệu</Text>;
        }}
      />
    </View>
  );
}

export const PopupView = forwardRef(function PopupView(
  data: {methods: any},
  ref: any,
) {
  const {methods} = data;
  const bottomSheetRef = useRef<any>(null); // refBBVottomSheet

  const [isLoading, setLoading] = useState(false);

  const data1 = [
    {
      Id: '32',
      Name: 'Item 32',
      Img: null,
      Price: 100,
    },
    {
      Id: '131',
      Name: 'Item 131',
      Img: null,
      Price: 100,
    },
    {
      Id: '31341231',
      Name: 'Item 31341231',
      Img: null,
      Price: 100,
    },
  ];
  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 75,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: '#fff',
      }}>
      <FPopup ref={bottomSheetRef} />
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          flexDirection: 'row',
          paddingVertical: 4,
        }}
        title={`Danh sách`}
        prefix={<View />}
        action={
          <TouchableOpacity
            onPress={() => closePopup(ref)}
            style={{padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        }
      />
      <FlatList
        data={data1}
        nestedScrollEnabled
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          gap: 16,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}
        renderItem={({item, index}) => {
          return (
            <DefaultProduct
              flexDirection="row"
              noDivider
              containerStyle={{
                paddingHorizontal: 16,
              }}
              actionView={
                <View style={{flexDirection: 'row'}}>
                  <AppButton
                    backgroundColor={'transparent'}
                    borderColor="transparent"
                    onPress={() => {
                      methods.setValue('products', [
                        ...methods.watch('products'),
                        {...item, Quantity: 1},
                      ]);
                      closePopup(ref);
                    }}
                    containerStyle={{
                      borderRadius: 100,
                      padding: 6,
                      height: 32,
                      width: 32,
                      backgroundColor: 'transparent',
                    }}
                    title={
                      <Winicon
                        src="outline/layout/circle-plus"
                        size={20}
                        color={ColorThemes.light.primary_main_color}
                      />
                    }
                  />
                </View>
              }
              data={item}
            />
          );
        }}
        style={{width: '100%', height: '100%'}}
        keyExtractor={item => item.Id.toString()}
        ListEmptyComponent={() => {
          if (isLoading) {
            return <SkeletonPlaceCard />;
          }
          return <Text style={{color: '#000000'}}>Không có dữ liệu</Text>;
        }}
      />
      <WScreenFooter
        style={{
          flexDirection: 'row',
          gap: 8,
          paddingHorizontal: 16,
          paddingBottom: 16,
        }}>
        <AppButton
          title={'Thêm mới'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            showPopup({
              ref: bottomSheetRef,
              enableDismiss: true,
              children: (
                <PopupCreateView
                  ref={bottomSheetRef}
                  onDone={(vl: any) => {
                    methods.setValue('products', [
                      ...methods.watch('products'),
                      {
                        ...vl,
                        Quantity: vl.Quantity ?? 1,
                        Img: null,
                        Price: 1000,
                      },
                    ]);
                    closePopup(ref);
                  }}
                />
              ),
            });
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
});

export const PopupCreateView = forwardRef(function PopupCreateView(
  data: {onDone?: any},
  ref: any,
) {
  const {onDone} = data;

  const methodProduct = useForm<any>({
    shouldFocusError: false,
    defaultValues: {Id: new Date().getTime().toString(), Quantity: 1},
  });

  const _onSubmit = async (ev: any) => {
    if (ev.Quantity == undefined || ev.Quantity == 0) {
      showSnackbar({
        message: 'Vui lòng nhập số lượng',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    if (onDone) {
      onDone(ev);
    }
    closePopup(ref);
  };

  const _onError = (ev: any) => {
    console.log(ev);
  };

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 175,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: '#fff',
      }}>
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          flexDirection: 'row',
          paddingVertical: 4,
        }}
        title={`Thêm mới`}
        prefix={<View />}
        action={
          <TouchableOpacity
            onPress={() => closePopup(ref)}
            style={{padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        }
      />
      <KeyboardAvoidingView style={{flex: 1, paddingHorizontal: 16}}>
        <ScrollView style={{flex: 1}}>
          <View
            style={{
              gap: 20,
              width: '100%',
              alignItems: 'center',
            }}>
            <TextFieldForm
              label="Tên sản phẩm"
              required
              textFieldStyle={{padding: 16}}
              style={{width: '100%'}}
              register={methodProduct.register}
              control={methodProduct.control}
              errors={methodProduct.formState.errors}
              name="Name"
            />
            <TextFieldForm
              required
              control={methodProduct.control}
              name="Description"
              errors={methodProduct.formState.errors}
              label="Mô tả"
              style={{backgroundColor: ColorThemes.light.transparent}}
              textFieldStyle={{
                height: 100,
                paddingHorizontal: 16,
                paddingTop: 16,
                paddingBottom: 16,
                justifyContent: 'flex-start',
                backgroundColor: ColorThemes.light.transparent,
              }}
              textStyle={{textAlignVertical: 'top'}}
              numberOfLines={10}
              multiline={true}
              onBlur={value => {
                if (value == undefined || value.length == 0) {
                  methodProduct.setError('TextArea', {
                    message: 'Vui lòng nhập thông tin mô tả',
                  });
                } else {
                  methodProduct.clearErrors('TextArea');
                }
              }}
              register={methodProduct.register}
            />
            <NumberPicker
              initValue={methodProduct.watch('Quantity')}
              buttonStyle={{width: 24, height: 24}}
              titleStyle={{...TypoSkin.title3}}
              onChange={value => {
                methodProduct.setValue('Quantity', value);
              }}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      <WScreenFooter
        style={{
          flexDirection: 'row',
          gap: 8,
          paddingHorizontal: 16,
          paddingBottom: 16,
        }}>
        <AppButton
          title={'Thêm'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={methodProduct.handleSubmit(_onSubmit, _onError)}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
});
