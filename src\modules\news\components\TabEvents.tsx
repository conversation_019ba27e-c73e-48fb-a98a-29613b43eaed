import React, {useEffect, useMemo, useCallback} from 'react';
import {
  StyleSheet,
  View,
  ActivityIndicator,
  FlatList,
  ListRenderItem,
  ScrollView,
  Text,
} from 'react-native';
import {SectionHeader} from './TabEventComponents/components/SectionHeader';
import {EventCardHorizontal, EventCardVertical} from '../card/EventCards';
import {useDispatch, useSelector} from 'react-redux';
import {RootState, AppDispatch} from '../../../redux/store/store';
import {NewsEvent} from '../../../redux/models/newsEvent';
import {ColorThemes} from '../../../assets/skin/colors';
import {fetchNewsEvents} from '../../../redux/actions/newEventAction';
import {isCurrentTimeInRange} from '../../../utils/Utils';

const TabEvents: React.FC = () => {
  const dispatch: AppDispatch = useDispatch();
  const {data, loading} = useSelector((state: RootState) => state.newsEvent);

  useEffect(() => {
    dispatch(fetchNewsEvents());
  }, [dispatch]);

  const ongoingEvents = useMemo(
    () => data.filter(e => isCurrentTimeInRange(e.DateStart, e.DateEnd)),
    [data],
  );

  const renderHorizontalItem: ListRenderItem<NewsEvent> = useCallback(
    ({item}) => <EventCardHorizontal item={item} />,
    [],
  );

  const renderVerticalItem: ListRenderItem<NewsEvent> = useCallback(
    ({item}) => <EventCardVertical item={item} />,
    [],
  );

  const ListHeader = useMemo(
    () => (
      <View>
        <SectionHeader title="Đang diễn ra" showSeeMore={true} />
        <FlatList
          data={ongoingEvents}
          renderItem={renderHorizontalItem}
          keyExtractor={item => item.Id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.hList}
        />
      </View>
    ),
    [ongoingEvents, renderHorizontalItem],
  );

  // đang tải dữ liệu
  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator
          size="large"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }

  // không có dữ liệu
  if (data.length === 0) {
    return (
      <View style={styles.center}>
        <Text>Không có sự kiện</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {ongoingEvents.length > 0 && ListHeader}

      {/* All Events Section */}
      {data.length > 0 && (
        <View>
          <SectionHeader title="Tất cả sự kiện" />
          <View style={styles.vList}>
            <FlatList
              data={data}
              renderItem={renderVerticalItem}
              keyExtractor={item => item.Id}
            />
          </View>
        </View>
      )}
    </ScrollView>
  );
};

export default TabEvents;

const styles = StyleSheet.create({
  container: {flexGrow: 1, paddingBottom: 16},
  center: {flex: 1, justifyContent: 'center', alignItems: 'center'},
  hList: {paddingLeft: 16, paddingRight: 8, paddingBottom: 16},
  vList: {paddingHorizontal: 16},
});
