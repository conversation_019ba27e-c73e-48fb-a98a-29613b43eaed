import React from 'react';
import {View, StyleSheet, ScrollView, Alert} from 'react-native';
import ProductCarousel from '../../components/ProductCarousel';
import {ProductItem} from '../../modules/Product/card/ProductCard';
import {SafeAreaView} from 'react-native-safe-area-context';
import ScreenHeader from '../Layout/header';
import {ColorThemes} from '../../assets/skin/colors';

// Dữ liệu mẫu cho sản phẩm nổi bật
const featuredProducts: ProductItem[] = [
  {
    id: '1',
    title: 'Lorem Ipsum is simply dummy text',
    price: 680.99,
    originalPrice: 780.99,
    discount: 20,
    image: {uri: 'https://i.imgur.com/UPrs1EH.jpg'},
    rating: 5,
    soldCount: 200,
  },
  {
    id: '2',
    title: 'Lorem Ipsum is simply dummy text',
    price: 680.99,
    image: {uri: 'https://i.imgur.com/UPrs1EH.jpg'},
    rating: 4.5,
    soldCount: 200,
  },
  {
    id: '3',
    title: '<PERSON><PERSON><PERSON> rửa mặt dưỡng ẩm cho da khô và nhạy cảm',
    price: 450.99,
    originalPrice: 550.99,
    discount: 15,
    image: {uri: 'https://i.imgur.com/UPrs1EH.jpg'},
    rating: 4.8,
    soldCount: 150,
  },
  {
    id: '4',
    title: 'Kem dưỡng da chống lão hóa ban đêm',
    price: 890.99,
    image: {uri: 'https://i.imgur.com/UPrs1EH.jpg'},
    rating: 4.7,
    soldCount: 300,
  },
];

// Dữ liệu mẫu cho sản phẩm mới
const newProducts: ProductItem[] = [
  {
    id: '5',
    title: 'Serum vitamin C làm sáng da',
    price: 520.99,
    originalPrice: 620.99,
    discount: 10,
    image: {uri: 'https://i.imgur.com/UPrs1EH.jpg'},
    rating: 4.6,
    soldCount: 180,
  },
  {
    id: '6',
    title: 'Mặt nạ dưỡng ẩm chuyên sâu',
    price: 320.99,
    image: {uri: 'https://i.imgur.com/UPrs1EH.jpg'},
    rating: 4.3,
    soldCount: 250,
  },
  {
    id: '7',
    title: 'Tẩy tế bào chết chiết xuất từ trà xanh',
    price: 280.99,
    originalPrice: 350.99,
    discount: 25,
    image: {uri: 'https://i.imgur.com/UPrs1EH.jpg'},
    rating: 4.9,
    soldCount: 220,
  },
  {
    id: '8',
    title: 'Xịt khoáng dưỡng ẩm và làm dịu da',
    price: 180.99,
    image: {uri: 'https://i.imgur.com/UPrs1EH.jpg'},
    rating: 4.4,
    soldCount: 320,
  },
];

const ProductCarouselDemo = () => {
  // Xử lý sự kiện khi nhấn vào sản phẩm
  const handleProductPress = (product: ProductItem) => {
    Alert.alert('Thông báo', `Bạn đã chọn sản phẩm: ${product.title}`);
  };

  // Xử lý sự kiện khi nhấn vào nút "Xem tất cả"
  const handleSeeAll = (category: string) => {
    Alert.alert('Thông báo', `Xem tất cả sản phẩm trong danh mục: ${category}`);
  };

  // Xử lý sự kiện khi thêm sản phẩm vào giỏ hàng
  const handleAddToCart = (product: ProductItem) => {
    Alert.alert('Thông báo', `Đã thêm sản phẩm vào giỏ hàng: ${product.title}`);
  };

  // Xử lý sự kiện khi thêm sản phẩm vào danh sách yêu thích
  const handleFavoritePress = (product: ProductItem) => {
    Alert.alert(
      'Thông báo',
      `Đã thêm sản phẩm vào danh sách yêu thích: ${product.title}`,
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScreenHeader title="Danh mục sản phẩm" />
      <ScrollView style={styles.container}>
        {/* Carousel sản phẩm nổi bật */}
        <ProductCarousel
          title="Sản phẩm nổi bật"
          products={featuredProducts}
          onSeeAll={() => handleSeeAll('Sản phẩm nổi bật')}
          onProductPress={handleProductPress}
          onAddToCart={handleAddToCart}
          onFavoritePress={handleFavoritePress}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
});

export default ProductCarouselDemo;
