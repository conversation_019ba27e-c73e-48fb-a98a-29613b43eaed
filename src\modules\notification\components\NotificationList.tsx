import React, {useMemo} from 'react';
import {View, Text, StyleSheet, SectionList} from 'react-native';
import {format, isToday, isYesterday, parseISO} from 'date-fns';
import {vi} from 'date-fns/locale';
import {NotificationItem} from '../../../redux/models/notification';
import NotificationCard from './card/NotificationCard';

interface NotificationListProps {
  notifications: NotificationItem[];
  loading?: boolean;
}

const NotificationList = ({notifications}: NotificationListProps) => {
  const sections = useMemo(() => {
    const grouped = notifications.reduce((acc: any, notification: any) => {
      const date = parseISO(notification.createdAt);
      let title = '';

      if (isToday(date)) {
        title = 'Hôm nay';
      } else if (isYesterday(date)) {
        title = 'Hôm qua';
      } else {
        title = format(date, 'dd/MM/yyyy', {locale: vi});
      }

      if (!acc[title]) {
        acc[title] = [];
      }
      acc[title].push(notification);
      return acc;
    }, {});

    return Object.keys(grouped).map(title => ({
      title: title,
      data: grouped[title],
    }));
  }, [notifications]);

  if (!notifications || notifications.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>Không có thông báo nào.</Text>
      </View>
    );
  }

  return (
    <SectionList
      sections={sections}
      keyExtractor={item => item.id}
      renderItem={({item}) => <NotificationCard item={item} />}
      renderSectionHeader={({section: {title}}) => (
        <Text style={styles.sectionHeader}>{title}</Text>
      )}
      contentContainerStyle={styles.listContainer}
      ItemSeparatorComponent={() => <View style={{height: 10}} />}
    />
  );
};

const styles = StyleSheet.create({
  listContainer: {
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: '600',
    color: '#8A8A8E',
    marginBottom: 12,
    marginTop: 20,
  },
  itemContainer: {
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 30,
    height: '100%',
    borderRadius: 8,
    marginRight: 12,
  },
  // Style cho emoji
  emoji: {
    fontSize: 20, // Chỉnh kích thước emoji tại đây
  },
  contentContainer: {
    flex: 1,
  },
  text: {
    fontSize: 15,
    color: '#0D1C2E',
    lineHeight: 22,
    fontWeight: '500',
  },
  timestamp: {
    fontSize: 13,
    color: '#6c757d',
    marginTop: 4,
  },
  unreadDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#FF3B30',
    marginLeft: 10,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#8A8A8E',
  },
});

export default NotificationList;
