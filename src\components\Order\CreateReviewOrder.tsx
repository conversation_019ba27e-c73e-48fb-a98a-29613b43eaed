import React, {useCallback, useState} from 'react';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import {Text, TextInput} from 'react-native-paper';
import {Rating, Winicon} from 'wini-mobile-components';
import {TextFieldForm} from '../../modules/news/form/component-form';
import {useForm} from 'react-hook-form';
import {ColorThemes} from '../../assets/skin/colors';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
  faAngleLeft,
  faExclamationTriangle,
  faLocationDot,
  faStar,
} from '@fortawesome/free-solid-svg-icons';
import {ScrollView} from 'react-native-gesture-handler';
import Footer from '../../Screen/Layout/footer';
import WScreenFooter from '../../Screen/Layout/footer';
import ImageCropPicker from 'react-native-image-crop-picker';
import {BaseDA} from '../../base/BaseDA';
const CreateReviewOrderDetail = () => {
  const methods = useForm({shouldFocusError: false});
  const [listImage, setListImage] = useState<any[]>([]);

  let textFieldStyle = {
    height: 70,
    paddingLeft: 8,
    paddingRight: 8,
    borderWidth: 0,
  };
  const {
    control,
    handleSubmit,
    setValue,
    formState: {errors},
    watch,
  } = useForm<any>({
    defaultValues: {
      image: null,
      value: 0,
      Content: '',
    },
  });

  const onSubmit = (data: any) => {
    console.log('data', data);
  };
  const pickerImg = useCallback(async () => {
    try {
      const img = await ImageCropPicker.openPicker({
        multiple: true,
        cropping: false,
        includeBase64: true,
      });

      if (img) {
        img.forEach((item: any) => {
          setListImage(prev => [...prev, item?.path]);
        });
        // const resImgs = await BaseDA.uploadFiles([
        //   {
        //     uri: img[0].path,
        //     type: img[0].mime,
        //     name: img[0].filename ?? 'file',
        //   },
        // ]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
    }
  }, [setValue]);

  const handleDeleteImage = (item: any) => {
    setListImage(prev => prev.filter((img: any) => img !== item));
    console.log('check-ListImage', listImage);
  };
  return (
    <View style={{flex: 1}}>
      <ScrollView>
        <View style={styles.container}>
          <View style={styles.Reaction}>
            <View style={styles.header}>
              <Text
                style={{
                  fontSize: 20,
                  fontWeight: '500',
                  fontFamily: 'roboto',
                  color: ColorThemes.light.text_primary_color,
                }}>
                Đơn hàng #13412341
              </Text>
              <Text
                style={{
                  fontSize: 16,
                  fontWeight: 'bold',
                  color: ColorThemes.light.success_main_color,
                }}>
                Hoàn thành
              </Text>
            </View>
            <View>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Image
                  source={{
                    uri: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSgum4jdr9gwoxMNQnHosR0HZkM0SLfn177lg&s',
                  }}
                  style={styles.avatar}
                />
                <View>
                  <Text style={styles.name}>Lê an tiến</Text>
                  <View>
                    <Rating value={4} size={20} />
                  </View>
                </View>
              </View>
            </View>
          </View>
          <View style={{position: 'relative'}}>
            <View style={styles.WrapperInput}>
              <TextFieldForm
                control={methods.control}
                name="Content"
                placeholder="Nhập nội dung đánh giá"
                returnKeyType="done"
                textFieldStyle={textFieldStyle}
                errors={methods.formState.errors}
                register={methods.register}
                required
                style={watch('Content') ? styles.input : styles.inputAction}
                suffix={
                  <TouchableOpacity style={{padding: 12}} onPress={pickerImg}>
                    <Winicon src="outline/files/file-image" size={20} />
                  </TouchableOpacity>
                }
              />
            </View>
          </View>

          <View
            style={{
              flexDirection: 'row',
              gap: 10,
              display: 'flex',
              flexWrap: 'wrap',
              paddingBottom: 100,
            }}>
            {listImage &&
              listImage.length > 0 &&
              listImage.map((item: any) => {
                return (
                  <View style={{marginBottom: 10, marginTop: 10}}>
                    <Image
                      source={{
                        uri: item,
                      }}
                      style={{width: 121, height: 101, borderRadius: 5}}
                    />
                    <TouchableOpacity
                      style={{
                        position: 'absolute',
                        bottom: 0,
                        left: '50%',
                        transform: [{translateX: -10}, {translateY: 14}],
                      }}
                      onPress={() => handleDeleteImage(item)}>
                      <Winicon
                        src={'outline/layout/circle-xmark'}
                        size={30}
                        color={ColorThemes.light.infor_main_color}
                      />
                    </TouchableOpacity>
                  </View>
                );
              })}
          </View>
        </View>
      </ScrollView>
      <WScreenFooter style={{width: '100%', paddingHorizontal: 20}}>
        <TouchableOpacity
          style={styles.buyButton}
          onPress={handleSubmit(onSubmit)}>
          <Text style={styles.actionButtonText}>Gửi đánh giá</Text>
        </TouchableOpacity>
      </WScreenFooter>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {padding: 10, flex: 1, marginBottom: 10},
  Reaction: {
    flexDirection: 'column',
    gap: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    marginTop: 10,
  },
  avatar: {
    borderWidth: 5,
    borderRadius: 50,
    width: 50,
    height: 50,
    marginRight: 10,
    borderColor: 'white',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  name: {
    fontWeight: 'bold',
  },
  WrapperInput: {
    marginVertical: 10,
    width: '100%',
  },
  input: {
    borderRadius: 10,
    fontSize: 16,
    color: '#000',
    backgroundColor: 'white',
    borderColor: ColorThemes.light.neutral_main_background_color,
    borderWidth: 1,
  },
  inputAction: {
    borderRadius: 10,
    fontSize: 16,
    color: '#000',
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: 'red',
  },
  errorText: {
    color: '#ff0000',
    fontSize: 14,
    marginTop: 5,
  },
  buyButton: {
    backgroundColor: 'blue',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50,
    borderRadius: 30,
  },

  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default CreateReviewOrderDetail;
