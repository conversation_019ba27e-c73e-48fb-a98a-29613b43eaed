import React, {FC, useState, useEffect} from 'react';
import {StyleSheet, Text, View} from 'react-native';

interface CountdownTimerProps {
  TargetDate: number;
}

export const CountdownTimer: FC<CountdownTimerProps> = ({
  TargetDate,
}: CountdownTimerProps) => {
  const calculateTimeLeft = () => {
    const difference = TargetDate - Date.now();
    let timeLeft = {days: 0, hours: 0, minutes: 0};

    if (difference > 0) {
      timeLeft = {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / 1000 / 60) % 60),
      };
    }
    return timeLeft;
  };

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, [TargetDate]);

  return (
    <View style={styles.countdownContainer}>
      <View style={styles.countdownItem}>
        <Text style={styles.countdownValue}>{timeLeft.days}</Text>
        <Text style={styles.countdownLabel}>Days</Text>
      </View>
      <View style={styles.countdownSeparator} />
      <View style={styles.countdownItem}>
        <Text style={styles.countdownValue}>{timeLeft.hours}</Text>
        <Text style={styles.countdownLabel}>Hours</Text>
      </View>
      <View style={styles.countdownSeparator} />
      <View style={styles.countdownItem}>
        <Text style={styles.countdownValue}>{timeLeft.minutes}</Text>
        <Text style={styles.countdownLabel}>Minutes</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  countdownContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  countdownItem: {
    alignItems: 'center',
    marginHorizontal: 6,
  },
  countdownValue: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  countdownLabel: {
    color: 'white',
    fontSize: 12,
    marginTop: 2,
  },
  countdownSeparator: {
    width: 1,
    height: '50%',
    backgroundColor: 'rgba(255,255,255,0.3)',
  },
});
