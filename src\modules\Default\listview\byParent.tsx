import React from 'react';
import {View, Text, FlatList, ScrollView} from 'react-native';
import {ListTile, StepCircleProgress, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {<PERSON><PERSON><PERSON>kin} from '../../../assets/skin/typography';

interface Props {
  Id?: string;
  showStep?: boolean;
}

export default function DefaultByParent(props: Props) {
  const stepList = [
    {
      Id: '1',
      step: '1',
      Name: 'Item 1',
      listItem: [
        {Id: '1', Icon: 'outline/arrows/circle-arrow-right', Name: 'sub 1'},
        {Id: '2', Icon: 'outline/arrows/circle-arrow-right', Name: 'sub 2'},
      ],
    },
    {
      Id: '4',
      step: '2',
      Name: 'Item 1',
      listItem: [
        {Id: '1', Icon: 'outline/arrows/circle-arrow-right', Name: 'sub 3'},
        {Id: '2', Icon: 'outline/arrows/circle-arrow-right', Name: 'sub 4'},
      ],
    },
    {
      Id: '12',
      step: '3',
      Name: 'Item 1',
      listItem: [
        {Id: '1', Icon: 'outline/arrows/circle-arrow-right', Name: 'sub 3'},
        {Id: '2', Icon: 'outline/arrows/circle-arrow-right', Name: 'sub 4'},
      ],
    },
    {Id: '5', step: '2', Name: 'Item 2'},
    {Id: '6', step: '2', Name: 'Item 3'},
  ];

  return (
    <View style={{height: 'auto', width: '100%'}}>
      {stepList.map((item, index) => {
        return (
          <ListTile
            key={item.Id}
            style={{paddingHorizontal: 16, padding: 0, paddingBottom: 16}}
            leading={
              !props.showStep ? null : (
                <View
                  style={{
                    flex: 1,
                    alignItems: 'center',
                    gap: 8,
                  }}>
                  <View
                    style={{alignItems: 'center', justifyContent: 'center'}}>
                    <StepCircleProgress
                      showTitle={false}
                      size={32}
                      strokeWidth={3}
                      totalStep={3}
                      step={1}
                    />
                    <Text
                      style={{
                        position: 'absolute',
                        textAlign: 'center',
                        ...TypoSkin.label3,
                        color: ColorThemes.light.primary_main_color,
                      }}>
                      {item.step}
                    </Text>
                  </View>
                  <View
                    style={{
                      flex: 1,
                      width: 1,
                      backgroundColor:
                        ColorThemes.light.neutral_main_border_color,
                    }}
                  />
                </View>
              )
            }
            listtileStyle={{gap: 16, alignItems: 'flex-start'}}
            title={
              <Text
                style={{
                  ...TypoSkin.heading7,
                  fontWeight: '600',
                  color: ColorThemes.light.neutral_text_title_color,
                }}>
                {item.Name}
              </Text>
            }
            trailing={
              !props.showStep ? null : (
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.neutral_text_subtitle_color,
                  }}>
                  0/5
                </Text>
              )
            }
            subtitle={
              item.listItem?.length == 0 ? null : (
                <View style={{height: undefined, width: '100%'}}>
                  {item.listItem?.map((item, index) => {
                    return (
                      <View
                        key={index}
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingVertical: 10,
                          gap: 8,
                        }}>
                        <View
                          style={[
                            {
                              width: 16,
                              borderRadius: 100,
                            },
                          ]}>
                          {item.Icon ? (
                            <Winicon src={item.Icon} size={16} />
                          ) : (
                            <Text style={[{fontSize: 12, color: '#61616B'}]}>
                              *
                            </Text>
                          )}
                        </View>
                        <Text
                          style={[
                            TypoSkin.label4,
                            {color: ColorThemes.light.neutral_text_label_color},
                          ]}>
                          {item.Name}
                        </Text>
                      </View>
                    );
                  })}
                </View>
              )
            }
          />
        );
      })}
    </View>
  );
}
