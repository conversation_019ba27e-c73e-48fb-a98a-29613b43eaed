import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  Pressable,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  AppButton,
  Checkbox,
  closePopup,
  FBottomSheet,
  FDialog,
  FPopup,
  hideBottomSheet,
  showBottomSheet,
  showPopup,
  TextField,
  Winicon,
} from 'wini-mobile-components';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import {ColorThemes} from '../../../assets/skin/colors';
import WScreenFooter from '../../../Screen/Layout/footer';
import ScreenHeader from '../../../Screen/Layout/header';
import {PopupCreateView} from '../../Default/listview/byRelativeCount';
import {TypoSkin} from '../../../assets/skin/typography';
import {CustomerType, StorageContanst} from '../../../Config/Contanst';
import {useForm} from 'react-hook-form';
import {
  DefaultProduct,
  SkeletonPlaceCard,
} from '../../Default/card/defaultProduct';
import {RootScreen} from '../../../router/router';
import {useNavigation} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {Ultis} from '../../../utils/Utils';
import {CustomerDA} from '../../customer/da';

import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import EmptyPage from '../../../Screen/emptyPage';
import {ProductDA} from '../productDA';
import ProductCard from '../card/ProductCard';
import {CartActions} from '../../../redux/reducers/CartReducer';
import {AppDispatch} from '../../../redux/store/store';
import {useDispatch} from 'react-redux';

const {width} = Dimensions.get('window');

const ITEM_WIDTH = width * 0.45;
const ITEM_HEIGHT = ITEM_WIDTH * 2; // Tăng chiều cao một chút

export const SearchIndex = forwardRef(function SearchIndex(
  data: {id?: any},
  ref: any,
) {
  const [searchValue, setSearchValue] = useState('');
  const bottomSheetRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const filterMethods = useForm({shouldFocusError: false});

  const [dataCourse, setData] = useState<Array<any>>([]);
  const [isRefresh, setRefresh] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const {t} = useTranslation();
  const navigation = useNavigation<any>();

  const [page, setPage] = useState(1);
  const size = 10;

  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(false);
  const productDA = new ProductDA();
  const dispatch: AppDispatch = useDispatch();

  const searchProducts = useCallback(
    async (
      searchText: string,
      pageNumber: number = 1,
      isRefresh: boolean = false,
    ) => {
      if (!searchText.trim()) {
        setData([]);
        return;
      }

      try {
        if (pageNumber === 1) {
          isRefresh ? setRefresh(true) : setIsLoading(true);
        } else {
          setIsLoadingMore(true);
        }

        // Create search query for product name
        const query = `@Name: (*${searchText.trim()}*)`;

        const result = await productDA.getAllList(pageNumber, 20, query, [
          'Id',
          'Name',
          'Price',
          'Img',
          'Discount',
          'CustomerId',
        ]);

        if (result && result.code === 200) {
          const newProducts = result.data || [];

          if (pageNumber === 1) {
            setData(newProducts);
          } else {
            setData(prev => [...prev, ...newProducts]);
          }

          // Check if there's more data
          setHasMoreData(newProducts.length === 20);
        } else {
          if (pageNumber === 1) {
            setData([]);
          }
        }
        setRefresh(false);
      } catch (err) {
        console.error('Error searching products:', err);
        if (pageNumber === 1) {
          setData([]);
        }
      } finally {
        setIsLoading(false);
        setRefresh(false);
        setIsLoadingMore(false);
      }
    },
    [productDA],
  );

  const onRefresh = async () => {
    setPage(1);
    await searchProducts(searchValue);
  };

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <FDialog ref={dialogRef} />
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          paddingVertical: 4,
          paddingTop: 32,
        }}
        onBack={() => navigation.goBack()}
        title={'Tìm kiếm'}
        bottom={
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              paddingHorizontal: 16,
              height: 56,
              gap: 8,
              paddingTop: 8,
              paddingBottom: 16,
            }}>
            <TextField
              style={{paddingHorizontal: 16, flex: 1, height: 40}}
              onChange={async (vl: string) => {
                setSearchValue(vl.trim());
                await searchProducts(vl.trim());
              }}
              value={searchValue}
              placeholder="Tìm kiếm"
              prefix={
                <Winicon
                  src="outline/development/zoom"
                  size={14}
                  color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                />
              }
            />
          </View>
        }
      />
      <FlatList
        data={dataCourse}
        refreshControl={
          <RefreshControl
            refreshing={isRefresh}
            onRefresh={() => {
              onRefresh();
            }}
          />
        }
        contentContainerStyle={{
          gap: 16,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}
        renderItem={({item, index}) => {
          return (
            <ProductCard
              key={index}
              onPress={() => {
                navigation.push(RootScreen.ProductDetail, {id: item.Id});
              }}
              item={item}
              onAddToCart={() => {
                dispatch(CartActions.addItemToCart(item, 1));
              }}
              onFavoritePress={() => {}}
              width={ITEM_WIDTH}
              height={ITEM_HEIGHT}
            />
          );
        }}
        style={{
          width: '100%',
          height: '100%',
          paddingTop: 16,
          paddingHorizontal: 16,
        }}
        numColumns={2}
        keyExtractor={item => item.Id?.toString()}
        ListEmptyComponent={() => {
          if (isLoading) {
            return (
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  flexDirection: 'row',
                }}>
                <ActivityIndicator
                  color={ColorThemes.light.Primary_Color_Main}
                />
              </View>
            );
          }
          return <EmptyPage title="Không có dữ liệu" />;
        }}
      />
    </SafeAreaView>
  );
});

const PopupFilter = forwardRef(function PopupFilter(
  data: {
    filterMethods: any;
    onApply: (values: any) => void;
    selectedAttributes: [];
  },
  ref: any,
) {
  const {onApply, selectedAttributes} = data;
  const [selected, setSelected] = useState<Array<any>>([]);

  useEffect(() => {
    if (selectedAttributes.length) {
      setSelected(selectedAttributes);
    }
  }, [selectedAttributes.length, selectedAttributes]);

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 146,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        backgroundColor: '#fff',
      }}>
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          borderBottomColor: ColorThemes.light.Neutral_Background_Color_Main,
          borderBottomWidth: 0.5,
          shadowColor: 'rgba(0, 0, 0, 0.03)',
          shadowOffset: {
            width: 0,
            height: 4,
          },
          shadowRadius: 20,
          elevation: 20,
          shadowOpacity: 1,
        }}
        title={'Bộ lọc'}
        prefix={<View style={{width: 50}} />}
        action={
          <View
            style={{flexDirection: 'row', padding: 12, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              onClick={() => hideBottomSheet(ref)}
              size={20}
              color={ColorThemes.light.Neutral_Text_Color_Body}
            />
          </View>
        }
      />
      <View style={{flex: 1, paddingHorizontal: 16, paddingVertical: 16}}>
        <Text style={{...TypoSkin.label3, marginBottom: 8}}>Loại dịch vụ</Text>
        <FlatList
          data={[
            {Id: 1, Name: 'a'},
            {Id: 2, Name: 'b'},
          ]}
          scrollEnabled={false}
          renderItem={({item, index}) => (
            <TouchableOpacity
              key={index}
              onPress={() => {
                if (!selected.includes(item.Id)) {
                  setSelected([...selected, item.Id]);
                } else {
                  setSelected(selected.filter((id: any) => id !== item.Id));
                }
              }}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 8,
                width: '50%',
              }}>
              <Checkbox
                value={selected.includes(item.Id)}
                onChange={v => {
                  if (v) {
                    setSelected([...selected, item.Id]);
                  } else {
                    setSelected(selected.filter((id: any) => id !== item.Id));
                  }
                }}
              />
              <Text
                style={{
                  ...TypoSkin.subtitle3,
                  color: ColorThemes.light.Neutral_Text_Color_Title,
                }}>
                {item?.Name ?? '-'}
              </Text>
            </TouchableOpacity>
          )}
          //Setting the number of column
          numColumns={2}
          style={{width: '100%'}}
          contentContainerStyle={{gap: 16}}
          keyExtractor={(item, index) => index.toString()}
        />
      </View>
      <WScreenFooter
        style={{flexDirection: 'row', gap: 8, paddingHorizontal: 16}}>
        <AppButton
          title={'Làm mới'}
          backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            setSelected([]);
          }}
          textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
        />
        <AppButton
          title={'Áp dụng'}
          backgroundColor={ColorThemes.light.Primary_Color_Main}
          borderColor="transparent"
          containerStyle={{
            height: 40,
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            hideBottomSheet(ref);
            onApply({AttributeId: selected});
          }}
          textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
});
