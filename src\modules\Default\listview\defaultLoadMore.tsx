import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {AppButton, Winicon} from 'wini-mobile-components';
import {DefaultProduct, SkeletonPlaceCard} from '../card/defaultProduct';

interface Props {
  horizontal?: boolean;
  titleList?: string;
  isSeeMore?: boolean;
}

const data1 = [
  {
    Id: '1',
    Name: 'Item 1',
    Img: null,
    Description: 'Description 1',
    Price: 100,
    Rate: 4,
  },
  {
    Id: '2',
    Name: 'Item 1',
    Img: null,
    Description: 'Description 1',
    Price: 100,
    Rate: 4,
  },
  {
    Id: '3',
    Name: 'Item 1',
    Img: null,
    Description: 'Description 1',
    Price: 100,
    Rate: 4,
  },
  {
    Id: '4',
    Name: 'Item 1',
    Img: null,
    Description: 'Description 1',
    Price: 100,
    Rate: 4,
  },
  {
    Id: '5',
    Name: 'Item 1',
    Img: null,
    Description: 'Description 1',
    Price: 100,
    Rate: 4,
  },
  {
    Id: '6',
    Name: 'Item 1',
    Img: null,
    Description: 'Description 1',
    Price: 100,
    Rate: 4,
  },
  {
    Id: '7',
    Name: 'Item 1',
    Img: null,
    Description: 'Description 1',
    Price: 100,
    Rate: 4,
  },
  {
    Id: '8',
    Name: 'Item 1',
    Img: null,
    Description: 'Description 1',
    Price: 100,
    Rate: 4,
  },
  {
    Id: '9',
    Name: 'Item 1',
    Img: null,
    Description: 'Description 1',
    Price: 100,
    Rate: 4,
  },
];

export default function DefaultLoadmore(props: Props) {
  const [data, setData] = useState<Array<any>>([]);

  const [isLoading, setLoading] = useState(false);
  const [isRefresh, setRefresh] = useState(false);
  const [isLoadMore, setLoadMore] = useState(false);

  useEffect(() => {
    onData();
  }, []);

  const onData = () => {
    setLoading(true);
    setData([]);
    setTimeout(() => {
      setData(data1);
      setLoading(false);
    }, 2000);
  };

  const onRefresh = () => {
    setRefresh(true);
    setData([]);
    setTimeout(() => {
      setData(data1);
      setRefresh(false);
    }, 2000);
  };

  const handleLoadMore = () => {
    setLoadMore(true);
    setTimeout(() => {
      setData([...data, ...data1]);
      setLoadMore(false);
    }, 1000);
  };

  return (
    <View style={{width: '100%', height: props.horizontal ? 386 : undefined}}>
      {props.titleList ? (
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
            paddingBottom: 16,
            flexDirection: 'row',
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
          }}>
          <Text
            style={{
              ...TypoSkin.heading5,
              color: ColorThemes.light.neutral_text_title_color,
            }}>
            {props.titleList}
          </Text>
          {props.isSeeMore ? (
            <AppButton
              title={'See more'}
              containerStyle={{
                justifyContent: 'flex-start',
                alignSelf: 'baseline',
              }}
              backgroundColor={'transparent'}
              textStyle={TypoSkin.buttonText3}
              borderColor="transparent"
              suffixIconSize={16}
              suffixIcon={'outline/arrows/circle-arrow-right'}
              onPress={() => {}}
              textColor={ColorThemes.light.infor_main_color}
            />
          ) : null}
        </View>
      ) : null}
      <FlatList
        data={data}
        nestedScrollEnabled
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefresh}
            onRefresh={() => {
              onRefresh();
            }}
          />
        }
        contentContainerStyle={{
          gap: 16,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}
        renderItem={({item, index}) => {
          return (
            <DefaultProduct
              key={index}
              flexDirection="row"
              noDivider
              containerStyle={{
                paddingHorizontal: 16,
              }}
              actionView={
                <AppButton
                  backgroundColor={'transparent'}
                  borderColor="transparent"
                  onPress={() => {}}
                  containerStyle={{
                    borderRadius: 100,
                    padding: 6,
                    height: 32,
                    width: 32,
                    backgroundColor:
                      ColorThemes.light.neutral_main_background_color,
                  }}
                  title={<Winicon src="outline/layout/circle-plus" size={18} />}
                />
              }
              data={item}
            />
          );
        }}
        style={{width: '100%', height: '100%'}}
        keyExtractor={item => item.Id.toString()}
        horizontal={props.horizontal}
        onEndReachedThreshold={0.5}
        onEndReached={handleLoadMore}
        ListEmptyComponent={() => {
          if (isLoading) {
            return <SkeletonPlaceCard />;
          }
          if (isLoadMore) {
            return (
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  flexDirection: 'row',
                }}>
                <ActivityIndicator
                  color={ColorThemes.light.primary_main_color}
                />
              </View>
            );
          }
          return <Text style={{color: '#000000'}}>Không có dữ liệu</Text>;
        }}
      />
    </View>
  );
}
