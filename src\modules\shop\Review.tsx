/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    Animated,
    Dimensions,
    Linking,
    RefreshControl,
    StyleSheet,
    TouchableOpacity,
    View,
    Image,
    Text

} from 'react-native';

import HeaderShop from '../../components/shop/HeaderShop';
import NavigateShop from '../../components/shop/NavigateShop';

import SearchBar from '../../components/shop/Search'
import ReviewItem from '../../components/shop/ReviewItem';
const Review = () => {


    return (
        <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <HeaderShop />
            </View>
            <NavigateShop
                title={"shop"}
            />
            <ReviewItem />
        </View >
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        maxHeight: 120
    },

    navigator: {
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        borderBottomColor: "#00FFFF",
        paddingBottom: 18,
        borderBottomWidth: 0.5,
    },
    orderInfo: {
        display: "flex",
        marginLeft: 13,
    },
    title: {
        fontSize: 20
    },
    numberOrder: {
        fontSize: 15,
        marginTop: 10,
        color: "#999"
    }

});



export default Review
