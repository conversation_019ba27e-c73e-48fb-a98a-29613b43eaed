import * as React from 'react';
import {useEffect, useRef} from 'react';
import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {SVGRenderer, SvgChart} from '@wuba/react-native-echarts';
import * as echarts from 'echarts/core';
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>atter<PERSON><PERSON>} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
} from 'echarts/components';
import {formatNumberConvert} from '../../../utils/Utils';
import {TypoSkin} from '../../../assets/skin/typography';

export enum EChartType {
  // "area" | "line" | "donut" | "pie" | "radar" | "bar" | "scatter" | "bubble" | "polarArea" | "radialBar" | "heatmap" | "candlestick" | "boxPlot" | "rangeBar" | "rangeArea" | "treemap" | undefined
  line = 'line',
  bar = 'bar',
  horizontalBar = 'horizontal bar',
  scatter = 'scatter',
  bubble = 'bubble',
  radar = 'radar',
  area = 'area',
  doughnut = 'doughnut',
  pie = 'pie',
}

interface Props {
  style?: React.CSSProperties;
  legend?: 'left' | 'top' | 'right' | 'bottom';
  type: EChartType;
  datasets: Array<any>;
  xAxisName?: Array<any>;
  yAxisName?: Array<string>;
  unit?: string;
  onPressSelected?: (ev: any) => void;
}

// Register extensions
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  SVGRenderer,
  // ...
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
]);

// Initialize
function ChartComponent({option, style}: {option: any; style?: ViewStyle}) {
  const chartRef = useRef<any>(null);
  const E_WIDTH =
    typeof style?.width === 'number'
      ? style.width
      : Dimensions.get('window').width / 2;
  const E_HEIGHT =
    typeof style?.height === 'number'
      ? style.height
      : Dimensions.get('window').height / 3;

  useEffect(() => {
    let chart: any;
    if (chartRef.current) {
      chart = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        width: E_WIDTH,
        height: E_HEIGHT,
      });
      chart.setOption(option);
    }
    return () => chart?.dispose();
  }, [option]);

  return <SvgChart ref={chartRef} style={style} />;
}

export default function RenderChartByType(props: Props) {
  let option: any;
  switch (props.type) {
    case EChartType.line:
      option = {
        // legend: {
        //     left: 'left',
        //     icon: 'roundRect',
        //     borderRadius: 8,
        // },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              type: 'dotted',
            },
          },
          axisLabel: {
            formatter: (value: any) => formatNumberConvert(value),
          },
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: props.xAxisName,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        series: props.datasets.map(c => {
          return {
            data: c.value,
            type: props.type,
            // smooth: true,
            stack: 'Total',
            itemStyle: {color: c.color},
          };
        }),
      };
      break;
    case EChartType.bar:
      option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: props.xAxisName,
          axisLine: {
            lineStyle: {
              color: '#D7D7DB',
              width: 1,
            },
          },
          axisLabel: {
            color: '#61616B',
            fontSize: 12,
            lineHeight: 16,
          },
        },
        grid: {
          left: '1%',
          right: '11%',
          bottom: '0%',
          top: '20%',
          containLabel: true,
        },
        yAxis: {
          type: 'value',
          name: props.unit,
          nameTextStyle: {
            color: '#61616B',
            fontSize: 12,
            lineHeight: 16,
            align: 'right',
            padding: [0, 8, 0, 0],
          },
          axisLabel: {
            formatter: (value: any) => formatNumberConvert(value),
            color: '#61616B',
            fontSize: 12,
          },
        },
        series: props.datasets?.map(c => {
          return {
            data: c.value,
            type: props.type,
            itemStyle: {color: c.color, borderRadius: [2, 2, 0, 0]},
            barMinWidth: '10%',
            barMaxWidth: '10%',
          };
        }),
      };
      break;
    case EChartType.horizontalBar:
      option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        xAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              type: 'dotted',
            },
          },
          axisLabel: {
            formatter: (value: any) => formatNumberConvert(value),
          },
        },
        yAxis: {
          type: 'category',
          boundaryGap: false,
          data: props.yAxisName,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        series: props.datasets?.map(c => {
          return {
            data: c.value,
            type: 'bar',
            itemStyle: {color: c.color, borderRadius: [2, 2, 0, 0]},
            radius: '50%',
            barMinWidth: 8,
            // barWidth: 8,
            barMaxWidth: 80,
            barGap: 0.2,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          };
        }),
      };
      break;
    case EChartType.scatter:
      break;
    case EChartType.pie:
      option = {
        title: {
          text: props.datasets
            .map(e =>
              typeof e.value === 'number'
                ? e.value
                : e.value.reduce(({a, b}: any) => a + b, 0),
            )
            .reduce((a, b) => a + b, 0),
          left: 'center',
          top: 'center',
          textStyle: {
            fontSize: '2.4rem',
            fontWeight: 'bold',
            color: '#18181B',
            fontFamily: 'Inter',
          },
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)', // Shows only value and percentage
        },
        series: [
          {
            type: props.type,
            radius: '50%',
            padAngle: 1,
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 14,
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: props.datasets.map(e => {
              return {
                name: e.title,
                value: e.value,
                itemStyle: {color: e.color, borderRadius: 4},
              };
            }),
          },
        ],
      };
      break;
    case EChartType.doughnut:
      option = {
        title: {
          text: props.datasets
            .map(e =>
              typeof e.value === 'number'
                ? e.value
                : e.value.reduce(({a, b}: any) => a + b, 0),
            )
            .reduce((a, b) => a + b, 0),
          left: 'center',
          top: 'center',
        },
        tooltip: {
          trigger: 'item',
          position: [10, 10],
          formatter: '{b}: {d}%', // Shows only value and percentage
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '80%'],
            avoidLabelOverlap: true,
            padAngle: 1,
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
            },
            // labelLine: {
            //     show: false,
            // },
            data: props.datasets.map(e => {
              return {
                name: e.title,
                value: e.value,
                itemStyle: {color: e.color, borderRadius: 4},
              };
            }),
          },
        ],
      };
      break;
    case EChartType.bubble:
      break;
    case EChartType.radar:
      option = {
        // legend: {
        //     left: 'left',
        //     icon: 'roundRect',
        //     borderRadius: 8,
        // },
        radar: {
          // shape: 'circle',
          indicator: props.datasets.map(e => {
            return {
              // name: e[0],
              // max: Math.max(...e.slice(1))
            };
          }),
        },
        series: props.datasets?.map(c => {
          return {
            type: props.type,
            itemStyle: {color: c.color, borderRadius: [2, 2, 0, 0]},
            radius: '50%',
            barMinWidth: 8,
            // barWidth: 8,
            barMaxWidth: 80,
            barGap: 0.2,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          };
        }),
      };
      break;
    default:
      break;
  }

  const renderLegend = () => {
    return (
      <View style={[styles.legendContainer]}>
        {props.datasets.map((e: any) => (
          <TouchableOpacity
            onPress={
              e?.value
                ? () => {
                    if (props.onPressSelected) props.onPressSelected(e);
                  }
                : undefined
            }
            key={e.id}
            style={styles.legendItem}>
            <View style={[styles.legendColor, {backgroundColor: e.color}]} />
            <Text style={styles.legendText}>{`${e.name}: ${e.value}`}</Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  switch (props.legend) {
    case 'top':
      return (
        <View style={styles.container}>
          <View style={{flexDirection: 'row', gap: 12, marginBottom: 8}}>
            {props.datasets.map((e: any) => (
              <TouchableOpacity
                onPress={
                  e?.value
                    ? () => {
                        if (props.onPressSelected) props.onPressSelected(e);
                      }
                    : undefined
                }
                key={e.id}
                style={{...styles.legendItem, gap: 4}}>
                <View
                  style={[
                    styles.legendColor,
                    {
                      backgroundColor: e.color,
                      borderRadius: 2,
                      width: 10,
                      height: 10,
                    },
                  ]}
                />
                <Text style={{...TypoSkin.buttonText4}}>{`${e.name}`}</Text>
              </TouchableOpacity>
            ))}
          </View>
          <ChartComponent
            option={option}
            style={{
              width: Dimensions.get('screen').width - 64,
              aspectRatio: 1,
            }}
          />
        </View>
      );
    case 'bottom':
      return (
        <View style={styles.container}>
          <ChartComponent
            option={option}
            style={{
              width: '100%',
              aspectRatio: ['bar', 'line'].some(type =>
                props.type.includes(type),
              )
                ? 2
                : 1,
            }}
          />
          {renderLegend()}
        </View>
      );
    case 'left':
      return (
        <View style={styles.rowContainer}>
          <View style={styles.legendColumn}>{renderLegend()}</View>
          <ChartComponent
            option={option}
            style={{
              width: '100%',
              height: 170,
              aspectRatio: ['bar', 'line'].some(type =>
                props.type.includes(type),
              )
                ? 2
                : 1,
            }}
          />
        </View>
      );
    case 'right':
    default:
      return (
        <View style={[styles.rowContainer]}>
          <ChartComponent
            option={option}
            style={{
              width: '100%',
              height: 170,
              aspectRatio: 1,
            }}
          />
          <View style={styles.legendColumn}>{renderLegend()}</View>
        </View>
      );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 16,
  },
  rowContainer: {
    flexDirection: 'row',
    paddingTop: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  legendContainer: {
    alignItems: 'center',
    alignContent: 'center',
    flexWrap: 'wrap',
    flex: 1,
    gap: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  legendColor: {
    height: 8,
    width: 8,
    borderRadius: 4,
  },
  legendText: {
    flex: 1,
    fontSize: 14,
    color: '#18181B',
  },
  legendColumn: {
    flex: 1,
    gap: 16,
    alignItems: 'center',
    alignContent: 'center',
  },
});
