import React, { useState, useRef } from 'react';
import { View, StyleSheet, Dimensions, SafeAreaView } from 'react-native';
import PagerView from 'react-native-pager-view';
import { useNavigation } from '@react-navigation/native';
import { showSnackbar, ComponentStatus, FLoading } from 'wini-mobile-components';
import { ScreenNewHeader } from '../../Layout/header';
import StepIndicator from '../../../components/StepIndicator';
import Step1Introduction from './Step1Introduction';
import Step2PasswordVerification from './Step2PasswordVerification';
import Step3QRCodeSetup from './Step3QRCodeSetup';
import Step4VerificationCode from './Step4VerificationCode';
import { ColorThemes } from '../../../assets/skin/colors';

const { width } = Dimensions.get('window');

interface TwoFactorAuthScreenProps {}

const TwoFactorAuthScreen: React.FC<TwoFactorAuthScreenProps> = () => {
  const navigation = useNavigation();
  const pagerRef = useRef<PagerView>(null);
  
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [password, setPassword] = useState('');
  const [secretKey, setSecretKey] = useState('');
  const [qrCodeValue, setQRCodeValue] = useState('');
  const [verificationCode, setVerificationCode] = useState('');

  const totalSteps = 4;

  const goToNextStep = () => {
    if (currentStep < totalSteps - 1) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      pagerRef.current?.setPage(nextStep);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 0) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      pagerRef.current?.setPage(prevStep);
    }
  };

  const handleStep1Next = () => {
    goToNextStep();
  };

  const handleStep2Next = async (enteredPassword: string) => {
    if (!enteredPassword.trim()) {
      showSnackbar({
        message: 'Vui lòng nhập mật khẩu',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Verify password with API
      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setPassword(enteredPassword);
      
      // Generate secret key and QR code value
      const generatedSecretKey = generateSecretKey();
      const qrValue = generateQRCodeValue(generatedSecretKey);
      
      setSecretKey(generatedSecretKey);
      setQRCodeValue(qrValue);
      
      goToNextStep();
    } catch (error) {
      showSnackbar({
        message: 'Mật khẩu không chính xác',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStep3Next = () => {
    goToNextStep();
  };

  const handleStep4Complete = async (code: string) => {
    if (code.length !== 6) {
      showSnackbar({
        message: 'Vui lòng nhập đầy đủ mã xác thực',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Verify OTP code with API
      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setVerificationCode(code);
      
      showSnackbar({
        message: 'Bật xác thực 2 lớp thành công!',
        status: ComponentStatus.SUCCSESS,
      });
      
      // Navigate back or to success screen
      navigation.goBack();
    } catch (error) {
      showSnackbar({
        message: 'Mã xác thực không chính xác',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const generateSecretKey = (): string => {
    // Generate a random secret key for TOTP
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    let result = '';
    for (let i = 0; i < 32; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const generateQRCodeValue = (secret: string): string => {
    // Generate TOTP QR code value
    const issuer = 'Chainivo';
    const accountName = '<EMAIL>'; // TODO: Get from user data
    return `otpauth://totp/${encodeURIComponent(issuer)}:${encodeURIComponent(accountName)}?secret=${secret}&issuer=${encodeURIComponent(issuer)}`;
  };

  const getHeaderTitle = () => {
    switch (currentStep) {
      case 0:
        return 'QL 2 MFA';
      case 1:
      case 2:
      case 3:
        return 'Xác thực Authenticator';
      default:
        return 'QL 2 MFA';
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      goToPreviousStep();
    } else {
      navigation.goBack();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <FLoading visible={isLoading} />
      
      <ScreenNewHeader
        title={getHeaderTitle()}
        showBack={true}
        onBack={handleBack}
        showAction={false}
        showDivider={true}
      />

      {/* Step Indicator - Only show for steps 1, 2, 3 */}
      {currentStep > 0 && (
        <View style={styles.stepIndicatorContainer}>
          <StepIndicator currentStep={currentStep} totalSteps={3} />
        </View>
      )}

      <PagerView
        ref={pagerRef}
        style={styles.pagerView}
        initialPage={0}
        scrollEnabled={false}
      >
        <View key="step1" style={styles.pageContainer}>
          <Step1Introduction onNext={handleStep1Next} />
        </View>

        <View key="step2" style={styles.pageContainer}>
          <Step2PasswordVerification 
            onNext={handleStep2Next}
            isLoading={isLoading}
          />
        </View>

        <View key="step3" style={styles.pageContainer}>
          <Step3QRCodeSetup
            secretKey={secretKey}
            qrCodeValue={qrCodeValue}
            onNext={handleStep3Next}
          />
        </View>

        <View key="step4" style={styles.pageContainer}>
          <Step4VerificationCode
            onComplete={handleStep4Complete}
            isLoading={isLoading}
          />
        </View>
      </PagerView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  stepIndicatorContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  pagerView: {
    flex: 1,
  },
  pageContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
});

export default TwoFactorAuthScreen;
