import AsyncStorage from '@react-native-async-storage/async-storage';
import { mockCartItems } from '../mock/cartData';
import { removeDataToAsyncStorage } from './AsyncStorage';

// Key để lưu giỏ hàng vào AsyncStorage
const CART_STORAGE_KEY = '@chainivo_cart';

// Hàm khởi tạo giỏ hàng với dữ liệu mẫu
export const initializeCart = async () => {
  try {
    // Kiểm tra xem giỏ hàng đã tồn tại chưa
    const existingCart = await AsyncStorage.getItem(CART_STORAGE_KEY);
    
    if (!existingCart) {
      // Nếu chưa có giỏ hàng, khởi tạo với dữ liệu mẫu
      await AsyncStorage.setItem(CART_STORAGE_KEY, JSON.stringify(mockCartItems));
      console.log('Cart initialized with mock data');
    } else {
      console.log('Cart already exists, skipping initialization');
    }
  } catch (error) {
    console.error('Error initializing cart:', error);
  }
};

// Hàm xóa giỏ hàng (dùng cho mục đích test)
export const clearCartStorage = async () => {
  try {
    // await AsyncStorage.removeItem(CART_STORAGE_KEY);
   await removeDataToAsyncStorage(CART_STORAGE_KEY);
    console.log('Cart cleared from storage');
  } catch (error) {
    console.error('Error clearing cart from storage:', error);
  }
};
