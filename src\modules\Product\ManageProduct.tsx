/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StyleSheet, View, } from 'react-native';
import HeaderShop from '../../components/shop/HeaderShop';
import NavigateShop from '../../components/shop/NavigateShop';
import { Title } from '../../Config/Contanst';
import ManageItemProduct from '../../components/Product/ManageProduct';
const ManageProduct = () => {
    return (
        <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <HeaderShop />
            </View>
            <NavigateShop
                title={Title.MyProduct}
            />
            <ManageItemProduct />
        </View >
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        maxHeight: 120
    },

    icon: {
        width: 24,
        height: 24,
    },

});


export default ManageProduct
