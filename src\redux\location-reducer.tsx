import { PayloadAction, createSlice } from '@reduxjs/toolkit'

const locationSlice = createSlice({
    name: 'location',
    initialState: {
        latitude: 21.028511,
        longitude: 105.804817
    },
    reducers: {
        getCurrentLocation: (state, action: PayloadAction<any>) => {
            switch (action.payload.type) {
                case 'GET':
                    state = action.payload.data
                    break;
                default:
                    state = action.payload.data
                    break;
            }
        }
    },
})

const { getCurrentLocation } = locationSlice.actions

export default locationSlice.reducer
export { getCurrentLocation }