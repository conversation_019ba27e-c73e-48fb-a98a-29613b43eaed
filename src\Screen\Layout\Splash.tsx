import { useEffect, useRef } from 'react';
import {
  Animated,
  Dimensions,
  Easing,
  Image,
  StatusBar,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { ColorThemes } from '../../assets/skin/colors';

export const SplashScreen = () => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    // Animate logo appearance
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 800,
        easing: Easing.out(Easing.back(1.5)),
        useNativeDriver: true,
      }),
    ]).start();
  });

  return (
    <View style={styles.splashContainer}>
      <Animated.View
        style={[
          styles.logoContainer,
          {
            flex: 1,
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}>
        <Image
          source={require('../../assets/splash.png')}
          style={{ width: 256, height: 256 }}
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Splash Screen Styles
  splashContainer: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    paddingTop: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  splashText: {
    color: 'white',
    fontSize: 42,
    fontWeight: 'bold',
    marginTop: 20,
  },
  splashSubtext: {
    color: 'white',
    fontSize: 18,
    marginTop: 10,
  },
});
