import {ActivityIndicator, View} from 'react-native';
import ScreenHeader from '../../../Screen/Layout/header';
import React, {useEffect} from 'react';
import NotificationListChip from '../components/NotificationListChip';
import NotificationList from '../components/NotificationList';
import {ColorThemes} from '../../../assets/skin/colors';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';
import {useNotificationHook} from '../../../redux/hook/notificationHook';

const NotificationIndex = () => {
  const notificationHook = useNotificationHook();
  const {notifications, loading} = useSelector(
    (state: RootState) => state.notification,
  );

  useEffect(() => {
    setTimeout(() => {
      notificationHook.setData('loading', false);
    }, 2000);
  }, []);

  return (
    <View style={{flex: 1}}>
      <ScreenHeader title={'Thông báo'} />

      {loading ? (
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <ActivityIndicator
            size="large"
            color={ColorThemes.light.primary_color}
          />
        </View>
      ) : (
        <View style={{flex: 1}}>
          <View style={{paddingHorizontal: 16}}>
            <NotificationListChip count={5} onChange={() => {}} />
          </View>

          <View style={{flex: 1}}>
            <NotificationList notifications={notifications} />
          </View>
        </View>
      )}
    </View>
  );
};

export default NotificationIndex;
