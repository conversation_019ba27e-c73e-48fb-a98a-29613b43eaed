import { StyleSheet, View, Text, TouchableOpacity, ScrollView, TextInput } from "react-native";
import React, { useEffect, useState } from "react";
import HeaderShop from "../../components/shop/HeaderShop";
import NavigateShop from "../../components/shop/NavigateShop";
import { Winicon } from 'wini-mobile-components';
import { DataController } from "../../base/baseController";
import { LoadingUI } from "../../features/loading";
import { useSelectorShopState } from "../../redux/hook/shopHook ";
import categoryDA from "../category/categoryDA";
import { randomGID } from "../../utils/Utils";
import { ColorThemes } from "../../assets/skin/colors";

// Tab types for affiliate config
enum AffiliateTabType {
    DEFAULT = "DEFAULT",
    SHOP = "SHOP",
    CATEGORY = "CATEGORY"
}

const ConfigAffiliate = () => {
    const [activeTab, setActiveTab] = useState<AffiliateTabType>(AffiliateTabType.DEFAULT);
    const [shopSettings, setShopSettings] = useState({
        customerPurchase: '5',
        f1Reward: '10',
        f2Reward: '5',
        isEnabled: true
    });
    const [defaultReward, setDefaultReward] = useState<Array<any>>();
    const [shopReward, setShopReward] = useState<Array<any>>();
    const [hasShopRewardData, setHasShopRewardData] = useState(false);

    // Category tab states
    const [categories, setCategories] = useState<Array<any>>([]);
    const [selectedCategory, setSelectedCategory] = useState<string>('');
    const [categoryReward, setCategoryReward] = useState<Array<any>>([]);
    const [childCategoryRewards, setChildCategoryRewards] = useState<Array<any>>([]);
    const [hasCategoryRewardData, setHasCategoryRewardData] = useState(false);
    const [categoryLoading, setCategoryLoading] = useState(false);
    const [isEditingParentCategory, setIsEditingParentCategory] = useState(false);
    const [editingChildCategories, setEditingChildCategories] = useState<{ [key: string]: boolean }>({});

    const [tempValues, setTempValues] = useState<{ [key: string]: string }>({});
    const controller = new DataController('Reward');
    const shopController = new DataController('ShopReward');
    const categoryController = new DataController('ShopCate');
    const categoryDataAccess = new categoryDA();
    const [loading, setLoading] = useState(true);
    const shopInfo = useSelectorShopState().data;
    useEffect(() => {
        fetchDefaultReward();
    }, []);

    useEffect(() => {

        if (activeTab === AffiliateTabType.SHOP && defaultReward) {
            fetchShopReward();
        }
    }, [activeTab, defaultReward]);

    useEffect(() => {
        if (activeTab === AffiliateTabType.CATEGORY) {
            fetchCategories();
        }
    }, [activeTab]);

    useEffect(() => {
        if (activeTab === AffiliateTabType.CATEGORY && selectedCategory && defaultReward) {
            fetchCategoryReward(selectedCategory);
            fetchChildCategories(selectedCategory);
        }
    }, [activeTab, selectedCategory, defaultReward]);

    const fetchDefaultReward = async () => {
        const res = await controller.getListSimple(
            {
                query: '*',
                sortby: { BY: 'Sort', DIRECTION: 'ASC' },
            }
        );
        if (res.code === 200) {
            setDefaultReward(res.data);
        }
        setLoading(false);
    };
    //convert name header
    const convertNameHeader = (name: string) => {
        switch (name) {
            case 'Thưởng F0':
                return 'F0';
                break;
            case 'Thưởng F1':
                return 'F1';
                break;
            case 'Khách hàng':
                return 'KH';
                break;
            default:
                break;
        }
    };
    const fetchShopReward = async () => {
        if (!shopInfo[0]?.Id) return;

        setLoading(true);
        const res = await shopController.getListSimple({
            page: 1,
            size: 100,
            query: `@ShopId: {${shopInfo[0].Id}}`,
            returns: ['Id', 'Name', 'Percent'],
            sortby: { BY: 'Sort', DIRECTION: 'ASC' },
        });

        if (res.code === 200 && res.data.length > 0) {
            setShopReward(res.data);
            setHasShopRewardData(true);
        } else {
            // Nếu chưa có data trong ShopReward, sử dụng data từ Reward
            setShopReward(defaultReward?.map(item => ({ ...item, isEdit: false })) || []);
            setHasShopRewardData(false);
        }
        setLoading(false);
    };

    const insertAllToShopReward = async (updatedData: any[]) => {
        if (!shopInfo[0]?.Id) return false;

        const dataToInsert = updatedData.map(item => ({
            Id: randomGID(),
            DateCreated: new Date().getTime(),
            Name: item.Name,
            Percent: item.Percent,
            ShopId: shopInfo[0].Id,
            FilialId: item.FilialId,
            Sort: item.Sort,
            Status: 1
        }));

        const res = await shopController.add(dataToInsert);
        return res.code === 200;
    };

    const updateShopRewardItem = async (itemId: string, percent: number) => {
        const res = await shopController.edit([{
            Id: itemId,
            Percent: percent
        }]);
        return res.code === 200;
    };

    // Category functions
    const fetchCategories = async () => {
        try {
            const res = await categoryDataAccess.getCategories();
            if (res?.code === 200 && res.data.length > 0) {
                setCategories(res.data);
                if (!selectedCategory) {
                    setSelectedCategory(res.data.find((category: any) => !category.ParentId)?.Id || res.data[0].Id);
                }
            }
        } catch (error) {
            console.error('Error fetching categories:', error);
        }
    };

    const fetchChildCategories = async (parentId: string) => {
        try {
            const res = categories.filter((category) => category.ParentId === parentId);
            await fetchChildCategoryRewards(res);
        } catch (error) {
            console.error('Error fetching child categories:', error);
        }
    };

    const fetchCategoryReward = async (categoryId: string) => {
        if (!categoryId || !shopInfo[0]?.Id) return;

        setCategoryLoading(true);
        // Fetch from ShopCate table first
        const res = await categoryController.getListSimple({
            page: 1,
            size: 100,
            query: `@CategoryId: {${categoryId}} @ShopId: {${shopInfo[0].Id}}`,
            returns: ['Id', 'Name', 'Percent', 'CategoryId'],
            sortby: { BY: 'Sort', DIRECTION: 'ASC' },
        });

        if (res.code === 200 && res.data.length > 0) {
            setCategoryReward(res.data.map((item: any) => ({ ...item, isEdit: false })));
            setHasCategoryRewardData(true);
        } else {
            // Nếu chưa có data trong ShopCate, sử dụng data từ Default
            setCategoryReward(defaultReward?.map(item => ({ ...item, isEdit: false })) || []);
            setHasCategoryRewardData(false);
        }
        setCategoryLoading(false);
    };

    const fetchChildCategoryRewards = async (childCats: any[]) => {
        if (!childCats.length || !shopInfo[0]?.Id) return;

        const childRewards = [];
        for (const child of childCats) {
            const res = await categoryController.getListSimple({
                page: 1,
                size: 100,
                query: `@CategoryId: {${child.Id}} @ShopId: {${shopInfo[0].Id}}`,
                returns: ['Id', 'Name', 'Percent', 'CategoryId'],
                sortby: { BY: 'Sort', DIRECTION: 'ASC' },
            });

            if (res.code === 200 && res.data.length > 0) {
                childRewards.push({
                    categoryId: child.Id,
                    categoryName: child.Name,
                    rewards: res.data.map((item: any) => ({ ...item, isEdit: false })),
                    hasData: true
                });
            } else {
                childRewards.push({
                    categoryId: child.Id,
                    categoryName: child.Name,
                    rewards: defaultReward?.map(item => ({ ...item, isEdit: false })) || [],
                    hasData: false
                });
            }
        }
        setChildCategoryRewards(childRewards);
    };

    const insertAllToCategoryReward = async (categoryId: string, updatedData: any[]) => {
        if (!shopInfo[0]?.Id) return false;

        const dataToInsert = updatedData.map((item: any) => ({
            Id: randomGID(),
            DateCreated: new Date().getTime(),
            Name: item.Name,
            Percent: item.Percent,
            CategoryId: categoryId,
            ShopId: shopInfo[0].Id,
            FilialId: item.FilialId,
            Sort: item.Sort,
            Status: 1
        }));

        const res = await categoryController.add(dataToInsert);
        if(res && res.code === 200){
            //gán lại id mới cho cate child 
            const newChildRewards = childCategoryRewards.map((cr) => {
                if (cr.categoryId === categoryId) {
                    return {
                        ...cr,
                        rewards: cr.rewards.map((item: any) => {
                            const newItem = dataToInsert.find((newItem: any) => newItem.Name === item.Name);
                            return newItem ? { ...newItem, isEdit: false } : item;
                        })
                    };
                }
                return cr;
            });
            setChildCategoryRewards(newChildRewards);
            return true;
        }
        return false;
    };

    const updateCategoryRewardItem = async (itemId: string, percent: number) => {
        const res = await categoryController.edit([{
            Id: itemId,
            Percent: percent
        }]);
        return res.code === 200;
    };

    const updateAllCategoryRewards = async (updatedData: any[]) => {
        if (!shopInfo[0]?.Id) return false;

        const res = await Promise.all(
            updatedData.map((item: any) => (
                categoryController.edit([{
                    Id: item.Id,
                    Percent: item.Percent
                }])
            ))
        );
        return res.every((r) => r.code === 200);    
    };

    const handleShopSettingsChange = (field: string, value: string | boolean) => {
        setShopSettings(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleTempValueChange = (itemId: string, value: string) => {
        setTempValues(prev => ({
            ...prev,
            [itemId]: value
        }));
    };

    const handleEditPress = (item: any) => {
        setTempValues(prev => ({
            ...prev,
            [item.Id]: item.Percent.toString()
        }));

        if (activeTab === AffiliateTabType.SHOP) {
            // Update shopReward to set isEdit = true for this item
            setShopReward(prev => prev?.map(reward =>
                reward.Id === item.Id ? { ...reward, isEdit: true } : reward
            ) || []);
        } else if (activeTab === AffiliateTabType.CATEGORY) {
            // Update categoryReward to set isEdit = true for this item
            setCategoryReward(prev => prev?.map(reward =>
                reward.Id === item.Id ? { ...reward, isEdit: true } : reward
            ) || []);
        }
    };

    // Handle edit for parent category (all rewards at once)
    const handleEditParentCategory = () => {
        setIsEditingParentCategory(true);
        // Set temp values for all rewards
        categoryReward?.forEach((item: any) => {
            setTempValues(prev => ({
                ...prev,
                [item.Id || item.RewardId]: item.Percent.toString()
            }));
        });
    };

    // Handle edit for child category
    const handleEditChildCategory = (categoryId: string, rewards: any[]) => {
        setEditingChildCategories(prev => ({
            ...prev,
            [categoryId]: true
        }));
        // Set temp values for all rewards in this child category
        rewards.forEach((item: any) => {
            setTempValues(prev => ({
                ...prev,
                [`${categoryId}_${item.Id || item.RewardId}`]: item.Percent.toString()
            }));
        });
    };

    const handleSavePress = async (item: any) => {
        const newPercent = parseFloat(tempValues[item.Id] || '0');

        if (isNaN(newPercent) || newPercent < 0) {
            console.log('Vui lòng nhập số hợp lệ');
            return;
        }

        try {
            if (activeTab === AffiliateTabType.SHOP) {
                if (hasShopRewardData) {
                    // Nếu đã có data trong ShopReward, chỉ update item này
                    const success = await updateShopRewardItem(item.Id, newPercent);
                    if (success) {
                        setShopReward(prev => prev?.map(reward =>
                            reward.Id === item.Id ? { ...reward, Percent: newPercent, isEdit: false } : reward
                        ) || []);
                    }
                } else {
                    // Nếu chưa có data trong ShopReward, insert toàn bộ với giá trị đã thay đổi
                    const updatedData = shopReward?.map(reward =>
                        reward.Id === item.Id ? { ...reward, Percent: newPercent } : reward
                    ) || [];

                    const success = await insertAllToShopReward(updatedData);
                    if (success) {
                        setHasShopRewardData(true);
                        // Fetch lại data từ ShopReward
                        await fetchShopReward();
                    }
                }
            } else if (activeTab === AffiliateTabType.CATEGORY) {
                debugger;
                if (hasCategoryRewardData) {
                    // Nếu đã có data trong CategoryReward, chỉ update item này
                    const success = await updateCategoryRewardItem(item.Id, newPercent);
                    if (success) {
                        setCategoryReward(prev => prev?.map(reward =>
                            reward.Id === item.Id ? { ...reward, Percent: newPercent, isEdit: false } : reward
                        ) || []);
                    }
                } else {
                    // Nếu chưa có data trong CategoryReward, insert toàn bộ với giá trị đã thay đổi
                    const updatedData = categoryReward?.map(reward =>
                        reward.Id === item.Id ? { ...reward, Percent: newPercent } : reward
                    ) || [];

                    const success = await insertAllToCategoryReward(selectedCategory, updatedData);
                    if (success) {
                        setHasCategoryRewardData(true);
                        // Fetch lại data từ CategoryReward
                        await fetchCategoryReward(selectedCategory);
                    }
                }
            }
        } catch (error) {
            console.error('Error saving reward:', error);
            console.log('Có lỗi xảy ra khi lưu dữ liệu');
        }
    };

    const handleCancelPress = (item: any) => {
        if (activeTab === AffiliateTabType.SHOP) {
            setShopReward(prev => prev?.map(reward =>
                reward.Id === item.Id ? { ...reward, isEdit: false } : reward
            ) || []);
        } else if (activeTab === AffiliateTabType.CATEGORY) {
            setCategoryReward(prev => prev?.map(reward =>
                reward.Id === item.Id ? { ...reward, isEdit: false } : reward
            ) || []);
        }

        setTempValues(prev => {
            const newValues = { ...prev };
            delete newValues[item.Id];
            return newValues;
        });
    };

    // Handle save for parent category (all rewards at once)
    const handleSaveParentCategory = async () => {
        try {
            const updatedRewards = categoryReward?.map((item: any) => ({
                ...item,
                Percent: parseFloat(tempValues[item.Id || item.RewardId] || item.Percent.toString())
            })) || [];

            if (hasCategoryRewardData) {
                // Update all existing records
                const success = await updateAllCategoryRewards(updatedRewards);
                if (success) {
                    setIsEditingParentCategory(false);
                    // Clear temp values
                    categoryReward?.forEach((item: any) => {
                        setTempValues(prev => {
                            const newValues = { ...prev };
                            delete newValues[item.Id || item.RewardId];
                            return newValues;
                        });
                    });
                    // Fetch lại data để có Id mới nhất
                    await fetchCategoryReward(selectedCategory);
                }
            } else {
                // Insert all new records
                const success = await insertAllToCategoryReward(selectedCategory, updatedRewards);
                if (success) {
                    setHasCategoryRewardData(true);
                    setIsEditingParentCategory(false);
                    // Clear temp values
                    categoryReward?.forEach((item: any) => {
                        setTempValues(prev => {
                            const newValues = { ...prev };
                            delete newValues[item.Id || item.RewardId];
                            return newValues;
                        });
                    });
                    // Fetch lại data để có Id mới từ ShopCate
                    await fetchCategoryReward(selectedCategory);
                }
            }
        } catch (error) {
            console.error('Error saving parent category rewards:', error);
        }
    };

    // Handle cancel for parent category
    const handleCancelParentCategory = () => {
        setIsEditingParentCategory(false);
        // Clear temp values for parent category
        categoryReward?.forEach((item: any) => {
            setTempValues(prev => {
                const newValues = { ...prev };
                delete newValues[item.Id || item.RewardId];
                return newValues;
            });
        });
    };

    // Handle save for child category
    const handleSaveChildCategory = async (categoryId: string) => {
        try {
            const childReward = childCategoryRewards.find(cr => cr.categoryId === categoryId);
            if (!childReward) return;

            const updatedRewards = childReward.rewards.map((item: any) => ({
                ...item,
                Percent: parseFloat(tempValues[`${categoryId}_${item.Id || item.RewardId}`] || item.Percent.toString())
            }));

            if (childReward.hasData) {
                // Update existing records
                const success = await updateAllCategoryRewards(updatedRewards);
                if (success) {
                    setEditingChildCategories(prev => ({ ...prev, [categoryId]: false }));
                    // Clear temp values for this child category
                    const childReward = childCategoryRewards.find(cr => cr.categoryId === categoryId);
                    childReward?.rewards.forEach((item: any) => {
                        setTempValues(prev => {
                            const newValues = { ...prev };
                            delete newValues[`${categoryId}_${item.Id || item.RewardId}`];
                            return newValues;
                        });
                    });
                    // Fetch lại data để có Id mới nhất
                    await fetchChildCategories(selectedCategory);
                }
            } else {
                // Insert new records
                const success = await insertAllToCategoryReward(categoryId, updatedRewards);
                if (success) {
                    setEditingChildCategories(prev => ({ ...prev, [categoryId]: false }));
                    // Clear temp values for this child category
                    const childReward = childCategoryRewards.find(cr => cr.categoryId === categoryId);
                    childReward?.rewards.forEach((item: any) => {
                        setTempValues(prev => {
                            const newValues = { ...prev };
                            delete newValues[`${categoryId}_${item.Id || item.RewardId}`];
                            return newValues;
                        });
                    });
                    // Fetch lại data để có Id mới từ ShopCate
                    await fetchChildCategories(selectedCategory);
                }
            }
        } catch (error) {
            console.error('Error saving child category rewards:', error);
        }
    };

    // Handle cancel for child category
    const handleCancelChildCategory = (categoryId: string) => {
        setEditingChildCategories(prev => ({ ...prev, [categoryId]: false }));
        // Clear temp values for this child category
        const childReward = childCategoryRewards.find(cr => cr.categoryId === categoryId);
        childReward?.rewards.forEach((item: any) => {
            setTempValues(prev => {
                const newValues = { ...prev };
                delete newValues[`${categoryId}_${item.Id || item.RewardId}`];
                return newValues;
            });
        });
    };



    // Tab configuration
    const tabs = [
        {
            id: AffiliateTabType.DEFAULT,
            name: 'Mặc định'
        },
        {
            id: AffiliateTabType.SHOP,
            name: 'SHOP'
        },
        {
            id: AffiliateTabType.CATEGORY,
            name: 'Danh mục'
        }
    ];

    const renderTabContent = () => {
        switch (activeTab) {
            case AffiliateTabType.DEFAULT:
                return renderDefaultSettings();
            case AffiliateTabType.SHOP:
                return renderShopSettings();
            case AffiliateTabType.CATEGORY:
                return renderCategorySettings();
            default:
                return renderDefaultSettings();
        }
    };



    const renderDefaultSettings = () => {
        return (
            <ScrollView style={styles.tabContent}>
                {loading ? (
                    <LoadingUI isLoading={loading} />
                ) : defaultReward && defaultReward.length > 0 ? (
                    defaultReward.map((item: any) => (
                        <View key={item.Id} style={styles.commissionItem}>
                            <Text style={styles.commissionLabel}>{item.Name}</Text>
                            <View style={styles.percentageContainer}>
                                <Text style={styles.percentageInput}>
                                    {item.Percent}%
                                </Text>
                            </View>
                        </View>
                    ))
                ) : (
                    <View style={styles.emptyState}>
                        <Winicon
                            src="outline/business/money"
                            size={48}
                            color="#999"
                        />
                        <Text style={styles.emptyText}>Chưa có cấu hình mặc định</Text>
                    </View>
                )}
            </ScrollView>
        );
    };

    const renderCommissionRow = (item: any) => (
        <View key={item.Id} style={styles.commissionItem}>
            <Text style={styles.commissionLabel}>{item.Name}</Text>
            <View style={{ ...styles.percentageContainer, backgroundColor: '#fff', width: 150 }}>
                {item.isEdit ? (
                    <TextInput
                        value={tempValues[item.Id] || item.Percent.toString()}
                        onChangeText={(value: string) => handleTempValueChange(item.Id, value)}
                        style={styles.percentageInputEdit}
                        keyboardType="numeric"
                        autoFocus
                    />
                ) : (
                    <Text style={styles.percentageInput}>
                        {item.Percent}%
                    </Text>
                )}
            </View>
            <View style={styles.editActionsContainer}>
                {item.isEdit ? (
                    <>
                        <TouchableOpacity
                            style={styles.saveButton}
                            onPress={() => handleSavePress(item)}
                        >
                            <Text style={styles.saveButtonText}>✓</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={styles.cancelButton}
                            onPress={() => handleCancelPress(item)}
                        >
                            <Text style={styles.cancelButtonText}>✕</Text>
                        </TouchableOpacity>
                    </>
                ) : (
                    <TouchableOpacity
                        style={styles.editButton}
                        onPress={() => handleEditPress(item)}
                    >
                        <Winicon src="fill/user interface/i-edit" size={15} />
                    </TouchableOpacity>
                )}
            </View>
        </View>
    );

    const renderShopSettings = () => {
        return (
            <ScrollView style={styles.tabContent}>
                {loading ? (
                    <LoadingUI isLoading={loading} />
                ) : shopReward && shopReward.length > 0 ? (
                    shopReward.map((item: any) => renderCommissionRow(item))
                ) : (
                    <View style={styles.emptyState}>
                        <Winicon
                            src="outline/business/shop"
                            size={48}
                            color="#999"
                        />
                        <Text style={styles.emptyText}>Chưa có cấu hình shop</Text>
                    </View>
                )}

                {/* Toggle Switch - chỉ hiển thị khi có data */}
                {shopReward && shopReward.length > 0 && (
                    <View style={styles.toggleContainer}>
                        <Text style={styles.toggleLabel}>ON</Text>
                        <TouchableOpacity
                            style={[styles.toggleSwitch, shopSettings.isEnabled && styles.toggleSwitchActive]}
                            onPress={() => handleShopSettingsChange('isEnabled', !shopSettings.isEnabled)}
                        >
                            <View style={[
                                styles.toggleThumb,
                                shopSettings.isEnabled && styles.toggleThumbActive
                            ]} />
                        </TouchableOpacity>
                    </View>
                )}
            </ScrollView>
        );
    };

    const renderCategoryTags = () => (
        <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.categoryTagsContainer}
            contentContainerStyle={styles.categoryTagsContent}
        >
            {categories.filter((category) => !category.ParentId).map((category) => (
                <TouchableOpacity
                    key={category.Id}
                    style={[
                        styles.categoryTag,
                        selectedCategory === category.Id && styles.categoryTagSelected
                    ]}
                    onPress={() => setSelectedCategory(category.Id)}
                >
                    <Text style={[
                        styles.categoryTagText,
                        selectedCategory === category.Id && styles.categoryTagTextSelected
                    ]}>
                        {category.Name}
                    </Text>
                </TouchableOpacity>
            ))}
        </ScrollView>
    );

    const renderCategoryTable = () => (
        <View style={styles.categoryTableContainer}>
            {/* Table Header */}
            <View style={styles.tableHeader}>
                <View style={styles.tableHeaderFirstColumn}>
                    <Text style={styles.tableHeaderText}>Toàn danh mục</Text>
                </View>
                {defaultReward?.map((reward) => (
                    <View key={reward.Id} style={styles.tableHeaderColumn}>
                        <Text style={styles.tableHeaderText}>{convertNameHeader(reward.Name) || reward.Name}</Text>
                        <Winicon src="fill/arrows/chevron-down" size={12} color="#666" />
                    </View>
                ))}
                <View style={styles.tableHeaderColumn}>
                    <Text style={styles.tableHeaderText}>TT</Text>
                </View>
            </View>

            {/* Table Row */}
            <View style={styles.tableRow}>
                <View style={styles.tableFirstColumn}>
                    <Text style={styles.tableRowLabel}>Toàn danh mục</Text>
                </View>
                {isEditingParentCategory ? (
                    // Edit mode - show all inputs
                    <>
                        {categoryReward?.map((item) => (
                            <View key={item.Id || item.RewardId} style={styles.tableCell}>
                                <TextInput
                                    value={tempValues[item.Id || item.RewardId] || item.Percent.toString()}
                                    onChangeText={(value: string) => handleTempValueChange(item.Id || item.RewardId, value)}
                                    style={styles.tableCellInput}
                                    keyboardType="numeric"
                                />
                            </View>
                        ))}
                        <View style={styles.tableActions}>
                            <TouchableOpacity
                                style={styles.saveButton}
                                onPress={handleSaveParentCategory}
                            >
                                <Text style={styles.saveButtonText}>✓</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={styles.cancelButton}
                                onPress={handleCancelParentCategory}
                            >
                                <Text style={styles.cancelButtonText}>✕</Text>
                            </TouchableOpacity>
                        </View>
                    </>
                ) : (
                    // View mode - show percentages
                    <>
                        {categoryReward?.map((item) => (
                            <View key={item.Id || item.RewardId} style={styles.tableCell}>
                                <Text style={styles.tableCellText}>{item.Percent}%</Text>
                            </View>
                        ))}
                        <View style={styles.tableActions}>
                            <TouchableOpacity
                                style={styles.editButton}
                                onPress={handleEditParentCategory}
                            >
                                <Winicon src="fill/user interface/i-edit" size={15} />
                            </TouchableOpacity>
                        </View>
                    </>
                )}
            </View>
        </View>
    );

    const renderChildCategoriesTable = () => (
        <View style={styles.childCategoriesContainer}>

            <View key={'childReward.categoryId'} style={styles.categoryTableContainer}>
                {/* Child Category Header */}
                <View style={styles.tableHeader}>
                    <View style={styles.tableHeaderFirstColumn}>
                        <Text style={styles.tableHeaderText}>Danh mục</Text>
                    </View>
                    {defaultReward?.map((reward) => (
                        <View key={reward.Id} style={styles.tableHeaderColumn}>
                            <Text style={styles.tableHeaderText}>{ convertNameHeader(reward.Name) || reward.Name}</Text>
                            <Winicon src="fill/arrows/chevron-down" size={12} color="#666" />
                        </View>
                    ))}
                    <View style={styles.tableHeaderColumn}>
                        <Text style={styles.tableHeaderText}>TT</Text>
                    </View>
                </View>
                {childCategoryRewards.map((childReward) => (
                    <View key={childReward.categoryId} style={styles.tableRow}>
                        <View style={styles.tableFirstColumn}>
                            <Text style={styles.tableRowLabel}>{childReward.categoryName}</Text>
                        </View>
                        {editingChildCategories[childReward.categoryId] ? (
                            // Edit mode
                            <>
                                {childReward.rewards.map((item: any) => (
                                    <View key={item.Id || item.RewardId} style={styles.tableCell}>
                                        <TextInput
                                            value={tempValues[`${childReward.categoryId}_${item.Id || item.RewardId}`] || item.Percent.toString()}
                                            onChangeText={(value: string) => handleTempValueChange(`${childReward.categoryId}_${item.Id || item.RewardId}`, value)}
                                            style={styles.tableCellInput}
                                            keyboardType="numeric"
                                        />
                                    </View>
                                ))}
                                <View style={styles.tableActions}>
                                    <TouchableOpacity
                                        style={styles.saveButton}
                                        onPress={() => handleSaveChildCategory(childReward.categoryId)}
                                    >
                                        <Text style={styles.saveButtonText}>✓</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={styles.cancelButton}
                                        onPress={() => handleCancelChildCategory(childReward.categoryId)}
                                    >
                                        <Text style={styles.cancelButtonText}>✕</Text>
                                    </TouchableOpacity>
                                </View>
                            </>
                        ) : (
                            // View mode
                            <>
                                {childReward.rewards.map((item: any) => (
                                    <View key={item.Id || item.RewardId} style={styles.tableCell}>
                                        <Text style={styles.tableCellText}>{item.Percent}%</Text>
                                    </View>
                                ))}
                                <View style={styles.tableActions}>
                                    <TouchableOpacity
                                        style={styles.editButton}
                                        onPress={() => handleEditChildCategory(childReward.categoryId, childReward.rewards)}
                                    >
                                        <Winicon src="fill/user interface/i-edit" size={15} />
                                    </TouchableOpacity>
                                </View>
                            </>
                        )}
                    </View>
                ))}
            </View>
        </View>
    );

    const renderCategorySettings = () => (
        <ScrollView style={styles.tabContent}>
            <Text style={{...styles.categoryDescription, color: '#000', fontWeight: 'bold', fontSize: 16, marginBottom: 0}}>
                Bảng cấu hình affiliate theo danh mục
            </Text>
            <Text style={{...styles.categoryDescription}}>
                Ưu tiên trả thưởng: Danh mục con → Danh mục lớn → SHOP → Mặc định
            </Text>

            {/* Category Tags */}
            {renderCategoryTags()}

            {/* Parent Category Table */}
            {categoryLoading ? (
                <LoadingUI isLoading={categoryLoading} />
            ) : categoryReward && categoryReward.length > 0 ? (
                renderCategoryTable()
            ) : (
                <View style={styles.emptyState}>
                    <Winicon
                        src="outline/business/tag"
                        size={48}
                        color="#999"
                    />
                    <Text style={styles.emptyText}>Chưa có cấu hình danh mục</Text>
                </View>
            )}

            {/* Child Categories Tables */}
            {!categoryLoading && childCategoryRewards.length > 0 ? (
                renderChildCategoriesTable()
            ) : !categoryLoading && categoryReward && categoryReward.length > 0 && (
                <View style={styles.emptyState}>
                    <Winicon
                        src="outline/business/tag"
                        size={48}
                        color="#999"
                    />
                    <Text style={styles.emptyText}>Không có danh mục con</Text>
                </View>
            )}
        </ScrollView>
    );

    return (
        <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <HeaderShop />
            </View>
            <NavigateShop
                title={'Cấu hình affiliate'}
            />

            {/* Tab Navigation */}
            <View style={styles.tabContainer}>
                {tabs.map((tab) => (
                    <TouchableOpacity
                        key={tab.id}
                        style={styles.tab}
                        onPress={() => setActiveTab(tab.id)}
                    >
                        <Text style={[
                            styles.tabText,
                            activeTab === tab.id && styles.activeTabText
                        ]}>
                            {tab.name}
                        </Text>
                        {activeTab === tab.id && <View style={styles.activeTabIndicator} />}
                    </TouchableOpacity>
                ))}
            </View>

            {/* Tab Content */}
            <View style={styles.content}>
                {renderTabContent()}
            </View>
        </View >
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        maxHeight: 120
    },
    navigator: {
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        borderBottomColor: "#00FFFF",
        paddingBottom: 18,
        borderBottomWidth: 0.5,
    },
    content: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    // Tab Navigation Styles
    tabContainer: {
        flexDirection: 'row',
        backgroundColor: '#fff',
        paddingHorizontal: 16,
        paddingTop: 16,
        paddingBottom: 0,
    },
    tab: {
        flex: 1,
        alignItems: 'center',
        paddingVertical: 12,
        paddingBottom: 16,
        position: 'relative',
    },
    tabText: {
        fontSize: 14,
        color: '#999',
        fontWeight: '400',
    },
    activeTabText: {
        color: '#00BFFF',
        fontWeight: '600',
    },
    activeTabIndicator: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        height: 2,
        backgroundColor: '#00BFFF',
    },
    // Content Styles
    tabContent: {
        flex: 1,
        padding: 16,
        backgroundColor: '#fff',
    },
    // Commission Item Styles
    commissionItem: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#fff',
        paddingVertical: 16,
        paddingHorizontal: 16,
        marginVertical: 8,
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    commissionLabel: {
        fontSize: 14,
        color: '#333',
        fontWeight: '400',
        flex: 1,
    },
    percentageContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f0f0f0',
        borderRadius: 6,
        paddingVertical: 10,

    },
    percentageInput: {
        fontSize: 11,
        color: '#333',
        fontWeight: '600',
        textAlign: 'left',
        minWidth: 200,
        borderWidth: 0,
        padding: 16,
    },
    // Empty State
    emptyState: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 16,
        color: '#999',
        textAlign: 'center',
    },
    // Toggle Switch Styles
    toggleContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        backgroundColor: '#fff',
        marginVertical: 10,
    },
    toggleLabel: {
        fontSize: 14,
        marginRight: 10,
        color: '#333',
        fontWeight: '400',
    },
    toggleSwitch: {
        width: 50,
        height: 30,
        borderRadius: 15,
        backgroundColor: '#ccc',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 5,
    },
    toggleSwitchActive: {
        backgroundColor: '#1C33FF',
    },
    toggleThumb: {
        width: 20,
        height: 20,
        borderRadius: 10,
        backgroundColor: '#fff',
    },
    toggleThumbActive: {
        transform: [{ translateX: 12 }],
    },
    // Edit Button Styles
    editButtonContainer: {
        alignItems: 'flex-end',
        marginBottom: 16,
    },
    editButton: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 4,
        minWidth: 50,
        alignItems: 'center',
    },
    editButtonText: {
        color: '#fff',
        fontSize: 12,
        fontWeight: '600',
    },
    actionButtonsContainer: {
        flexDirection: 'row',
        gap: 8,
    },
    saveButton: {
        backgroundColor: '#F5F5F5',
        paddingHorizontal: 8,
        paddingVertical: 6,
        borderRadius: 24,
        minWidth: 30,
        alignItems: 'center',
    },
    saveButtonText: {
        color: '#4CAF50',
        fontSize: 12,
        fontWeight: '600',
    },
    cancelButton: {
        backgroundColor: '#F5F5F5',
        paddingHorizontal: 8,
        paddingVertical: 6,
        borderRadius: 24,
        minWidth: 30,
        alignItems: 'center',
    },
    cancelButtonText: {
        color: '#F44336',
        fontSize: 12,
        fontWeight: '600',
    },
    percentageInputEdit: {
        fontSize: 14,
        color: '#333',
        fontWeight: '600',
        textAlign: 'center',
        minWidth: 150,
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#E8E8E8',
        borderRadius: 4,
        padding: 16,
    },
    editActionsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        marginLeft: 12,
    },
    // Category Tab Styles
    categoryDescription: {
        fontSize: 12,
        color: '#666',
        marginBottom: 16,
        lineHeight: 20,
    },
    categoryTagsContainer: {
        // marginBottom: 20,
    },
    categoryTagsContent: {
        // paddingHorizontal: 16,
        gap: 12,
    },
    categoryTag: {
        paddingHorizontal: 16,
        paddingVertical: 4,
        borderRadius: 20,
        backgroundColor: ColorThemes.light.neutral_main_background_color,
        height: 30,
    },
    categoryTagSelected: {
        backgroundColor: ColorThemes.light.primary_background,
    },
    categoryTagText: {
        fontSize: 14,
        color: '#666',
        fontWeight: '400',
    },
    categoryTagTextSelected: {
        color: ColorThemes.light.primary_main_color,
        // fontWeight: '600',
    },
    categoryTableContainer: {
        backgroundColor: '#fff',
        borderRadius: 8,
        overflow: 'hidden',
        marginTop: 16,
    },
    tableHeader: {
        flexDirection: 'row',
        paddingVertical: 12,
        // paddingHorizontal: 16,
    },
    tableHeaderText: {
        fontSize: 12,
        color: '#666',
        fontWeight: '600',
        textAlign: 'center',
    },
    tableHeaderFirstColumn: {
        flex: 2,
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
        paddingHorizontal: 8,
    },
    tableHeaderColumn: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 4,
    },
    tableRow: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 16,
        // paddingHorizontal: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    tableFirstColumn: {
        flex: 2,
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
        paddingHorizontal: 8,
    },
    tableRowLabel: {
        fontSize: 14,
        color: '#333',
        fontWeight: '400',
        textAlign: 'center',
    },
    tableCell: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        maxWidth: 100,
    },
    tableCellText: {
        fontSize: 14,
        color: '#333',
        fontWeight: '600',
    },
    tableCellInput: {
        fontSize: 14,
        color: '#333',
        fontWeight: '600',
        textAlign: 'center',
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#E8E8E8',
        borderRadius: 4,
        paddingHorizontal: 8,
        paddingVertical: 4,
        minWidth: 60,
    },
    tableActions: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        marginLeft: 12,
    },
    editCellContainer: {
        alignItems: 'center',
        gap: 8,
    },
    cellActions: {
        flexDirection: 'row',
        gap: 4,
        marginTop: 4,
    },
    cellContainer: {
        padding: 8,
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: 40,
    },
    childCategoriesContainer: {
        marginTop: 20,
        gap: 16,
    },

});

export default ConfigAffiliate


