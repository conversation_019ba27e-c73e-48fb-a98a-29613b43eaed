import React, {FC} from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image,
  ImageBackground,
} from 'react-native';
import {IconText} from '../components/TabEventComponents/components/IconText';
import {CountdownTimer} from '../components/TabEventComponents/components/CountdownTimer';
import {ColorThemes} from '../../../assets/skin/colors';
import {NewsEvent} from '../../../redux/models/newsEvent';
import {formatTimestamp} from '../../../utils/Utils';

interface EventCardProps {
  item: NewsEvent;
}

const defaultImage = require('../../../assets/images/default_img.png');

export const EventCardHorizontal: FC<EventCardProps> = ({item}) => (
  <TouchableOpacity style={styles.hCard}>
    <Image
      source={item.Img ? {uri: item.Img} : defaultImage}
      style={styles.hCardImage}
    />
    <View style={styles.hCardContent}>
      <Text
        style={[styles.hCardTitle, {color: ColorThemes.light.infor_text_color}]}
        numberOfLines={2}>
        {item.Title}
      </Text>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
        <Text
          style={[
            styles.hCardLocation,
            {color: ColorThemes.light.infor_text_color},
          ]}
          numberOfLines={1}>
          {item.Address}
        </Text>
        <TouchableOpacity style={styles.joinButton}>
          <Text style={styles.joinButtonText}>Tham gia</Text>
        </TouchableOpacity>
      </View>
    </View>
  </TouchableOpacity>
);

export const EventCardVertical: FC<EventCardProps> = ({item}) => (
  <TouchableOpacity style={styles.vCard}>
    {item.DateStart ? (
      <ImageBackground
        source={item.Img ? {uri: item.Img} : defaultImage}
        style={styles.vCardImage}
        imageStyle={{borderRadius: 8}}>
        <CountdownTimer TargetDate={item.DateStart} />
      </ImageBackground>
    ) : (
      <Image
        source={item.Img ? {uri: item.Img} : defaultImage}
        style={styles.vCardImage}
      />
    )}
    <View style={styles.vCardContent}>
      <Text
        style={[
          styles.vCardTitle,
          {color: ColorThemes.light.infor_text_color},
        ]}>
        {item.Title}
      </Text>
      <IconText
        icon={require('../../../assets/icons/pinmap.png')}
        text={item.Address}
      />
      <IconText
        icon={require('../../../assets/icons/calendar.png')}
        text={formatTimestamp(item.DateStart)}
      />
    </View>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  hCard: {
    width: 280,
    backgroundColor: 'white',
    borderRadius: 12,
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  hCardImage: {
    width: '100%',
    height: 140,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  hCardContent: {padding: 12},
  hCardTitle: {fontSize: 16, fontWeight: 'bold', marginBottom: 8},
  hCardLocation: {
    width: '60%',
    fontSize: 14,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  joinButton: {
    height: 26,
    width: 100,
    backgroundColor: '#FFC043',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  joinButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
  },
  vCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
    flexDirection: 'row',
    padding: 10,
  },
  vCardImage: {
    width: 140,
    borderRadius: 8,
    marginRight: 12,
  },
  vCardContent: {flex: 1, justifyContent: 'center'},
  vCardTitle: {
    fontSize: 16,
    marginBottom: 8,
  },
});
