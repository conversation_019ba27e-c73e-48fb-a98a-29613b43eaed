import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { ColorThemes } from '../assets/skin/colors';
import { TypoSkin } from '../assets/skin/typography';
import GiftCard from '../components/Gift/GiftCard';

// Demo data với layout nằm ngang
const demoGifts = [
  {
    Id: '1',
    Name: 'Combo mỹ phẩm nhập mỹ xách tay gồm: phấn kem nền, kẻ mắt...',
    Description: 'Set mỹ phẩm cao cấp',
    Img: 'demo-gift-1.jpg',
    Value: 250,
    Status: 1,
    Quantity: 2,
    Category: 'beauty',
    ExpriseDate: '2022-12-30', // Hết hạn
  },
  {
    Id: '2',
    Name: 'Voucher mua sắm 100K',
    Description: 'Voucher giảm giá',
    Img: 'demo-gift-2.jpg',
    Value: 500,
    Status: 1,
    Quantity: 10,
    Category: 'voucher',
    ExpriseDate: '2024-12-30',
  },
  {
    Id: '3',
    Name: 'Áo thun premium cotton',
    Description: '<PERSON><PERSON> thun chất lượng cao',
    Img: 'demo-gift-3.jpg',
    Value: 1000,
    Status: 1,
    Quantity: 0, // Hết hàng
    Category: 'fashion',
    ExpriseDate: '2024-12-30',
  },
  {
    Id: '4',
    Name: 'Tai nghe bluetooth cao cấp',
    Description: 'Tai nghe không dây',
    Img: 'demo-gift-4.jpg',
    Value: 300,
    Status: 1,
    Quantity: 5,
    Category: 'electronics',
    ExpriseDate: '2024-12-30',
  },
];

const HorizontalGiftCardDemo = () => {
  const currentPoints = 800; // Đủ điểm cho một số item

  const handleGiftPress = (gift: any) => {
    console.log('Gift pressed:', gift.Name);
  };

  const handleExchangeGift = (gift: any) => {
    console.log('Exchange gift:', gift.Name);
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Horizontal Gift Card Layout</Text>
      <Text style={styles.subtitle}>
        Điểm hiện tại: {currentPoints} - Test các trạng thái khác nhau
      </Text>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>✅ Có thể đổi (đủ điểm, còn hàng)</Text>
        <GiftCard
          item={demoGifts[1]} // 500 điểm, còn hàng
          onPress={handleGiftPress}
          onExchange={handleExchangeGift}
          currentPoints={currentPoints}
        />
        <GiftCard
          item={demoGifts[3]} // 300 điểm, còn hàng
          onPress={handleGiftPress}
          onExchange={handleExchangeGift}
          currentPoints={currentPoints}
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>❌ Không thể đổi (không đủ điểm)</Text>
        <GiftCard
          item={demoGifts[2]} // 1000 điểm, không đủ
          onPress={handleGiftPress}
          onExchange={handleExchangeGift}
          currentPoints={currentPoints}
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>⏰ Hết hạn</Text>
        <GiftCard
          item={demoGifts[0]} // Hết hạn 2022
          onPress={handleGiftPress}
          onExchange={handleExchangeGift}
          currentPoints={currentPoints}
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📦 Hết hàng</Text>
        <GiftCard
          item={demoGifts[2]} // Quantity = 0
          onPress={handleGiftPress}
          onExchange={handleExchangeGift}
          currentPoints={currentPoints}
        />
      </View>

      <View style={styles.designNotes}>
        <Text style={styles.notesTitle}>📝 Design Notes</Text>
        <Text style={styles.notesText}>
          • Layout nằm ngang với image 80x80px bên trái{'\n'}
          • Title hiển thị tối đa 2 dòng{'\n'}
          • SL (số lượng) format: "SL: 02"{'\n'}
          • Diamond icon màu vàng (#FFA500){'\n'}
          • Button "ĐỔI NGAY" màu cam{'\n'}
          • Responsive với các trạng thái khác nhau
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  title: {
    ...TypoSkin.heading1,
    textAlign: 'center',
    marginVertical: 20,
    color: ColorThemes.light.neutral_text_title_color,
  },
  subtitle: {
    ...TypoSkin.body1,
    textAlign: 'center',
    marginBottom: 20,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    ...TypoSkin.title2,
    color: ColorThemes.light.neutral_text_title_color,
    marginHorizontal: 16,
    marginBottom: 8,
    fontWeight: '600',
  },
  designNotes: {
    backgroundColor: 'white',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  notesTitle: {
    ...TypoSkin.title3,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 8,
    fontWeight: '600',
  },
  notesText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    lineHeight: 20,
  },
});

export default HorizontalGiftCardDemo;
