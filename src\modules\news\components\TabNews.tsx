import React from 'react';
import {SafeAreaView, FlatList, StyleSheet, View} from 'react-native';
import NewsCard from '../card/NewsCard';
import {useNavigation} from '@react-navigation/native';
import {NewsItem} from '../../../redux/models/news';

const DUMMY_NEWS: NewsItem[] = [
  {
    Id: 'card-001',
    Name: 'Khám phá Lập trình Hiện đại với TypeScript',
    Description:
      'Một bài viết giới thiệu về các tính năng nổi bật của TypeScript và lý do tại sao nó trở nên phổ biến trong cộng đồng lập trình viên.',
    Img: 'https://cdn-media.sforum.vn/storage/app/media/anh-dep-83.jpg',
    Content:
      'TypeScript là một siêu tập của JavaScript, đư<PERSON><PERSON> phát triển bởi Microsoft. <PERSON><PERSON> bổ sung các kiểu tĩnh và các tính năng hướng đối tượng vào JavaScript, giúp phát hiện lỗi sớm hơn và xây dựng các ứng dụng lớn một cách dễ dàng và an toàn hơn. Bài viết sẽ đi sâu vào các khái niệm như Interfaces, Generics và Decorators.',
    relativeUser: {
      image: 'https://i.pravatar.cc/150?u=a042581f4e29026704d',
      title: 'Nguyễn Văn An',
    },
    Likes: 10,
    Comment: 5,
    IsLike: true,
    IsBookmark: true,
    CreatedAt: '2025-06-12T08:00:00Z',
  },
  {
    Id: 'card-002',
    Name: 'Du lịch Vịnh Hạ Long - Kỳ quan thiên nhiên thế giới',
    Description:
      'Cẩm nang du lịch Vịnh Hạ Long từ A đến Z: thời điểm lý tưởng, phương tiện di chuyển, các địa điểm vui chơi và ẩm thực không thể bỏ lỡ.',
    Img: 'https://cdn-media.sforum.vn/storage/app/media/anh-dep-83.jpg',
    Content:
      'Vịnh Hạ Long, di sản thế giới được UNESCO công nhận, là điểm đến mơ ước của nhiều du khách. Bạn có thể thuê thuyền kayak để khám phá các hang động kỳ vĩ, ghé thăm làng chài Cửa Vạn để tìm hiểu văn hóa địa phương, hoặc đơn giản là thư giãn trên du thuyền và ngắm hoàng hôn. Đừng quên thưởng thức hải sản tươi sống tại đây!',
    relativeUser: {
      image: 'https://i.pravatar.cc/150?u=travelblogger01',
      title: 'Trần Thị Bích',
    },
    Likes: 10,
    Comment: 5,
    IsLike: true,
    IsBookmark: true,
    CreatedAt: '2025-06-11T10:30:00Z',
  },
  {
    Id: 'card-003',
    Name: 'Công thức làm Phở Bò chuẩn vị Hà Nội',
    Description:
      'Tự tay nấu một tô phở bò nóng hổi, đậm đà hương vị truyền thống ngay tại nhà với công thức đơn giản và chi tiết được chia sẻ từ bếp trưởng.',
    Img: 'https://cdn-media.sforum.vn/storage/app/media/anh-dep-83.jpg',
    Content:
      'Nguyên liệu cần chuẩn bị: xương bò, thịt bò, bánh phở, hành, gừng và các loại gia vị đặc trưng như quế, hồi, thảo quả. Bí quyết để có nước dùng trong và ngọt thanh là hầm xương ở lửa nhỏ trong ít nhất 8 tiếng và thường xuyên vớt bọt. ',
    relativeUser: {
      image: 'https://i.pravatar.cc/150?u=chefhoangyen',
      title: 'Bếp trưởng Yen',
    },
    Likes: 10,
    Comment: 5,
    IsLike: true,
    IsBookmark: true,
    CreatedAt: '2025-05-10T15:45:00Z',
  },
  {
    Id: 'card-004',
    Name: 'Áo Thun Cotton Basic - Trắng',
    Description:
      'Chất liệu 100% cotton thoáng mát, co giãn 4 chiều, phù hợp cho mọi hoạt động hàng ngày. Thiết kế tối giản, dễ dàng phối đồ.',
    Img: 'https://cdn-media.sforum.vn/storage/app/media/anh-dep-83.jpg',
    Content:
      'Thông tin chi tiết sản phẩm: Cổ tròn, tay ngắn. Bảng size: S (dưới 60kg), M (60-70kg), L (70-80kg). Hướng dẫn bảo quản: Giặt máy ở chế độ nhẹ, không dùng chất tẩy, phơi ở nơi thoáng mát để giữ form áo.',
    relativeUser: {
      image: 'https://i.pravatar.cc/150?u=fashion-brand-logo',
      title: 'The Basic House',
    },
    Likes: 10,
    Comment: 5,
    IsLike: true,
    IsBookmark: true,
    CreatedAt: '2024-03-17T09:15:00Z',
  },
  {
    Id: 'card-005',
    Name: 'Hội thảo "AI và Tương lai ngành Công nghệ"',
    Description:
      'Một sự kiện không thể bỏ lỡ dành cho các lập trình viên, nhà quản lý và những ai quan tâm đến sự phát triển của trí tuệ nhân tạo.',
    Img: 'https://cdn-media.sforum.vn/storage/app/media/anh-dep-83.jpg',
    Content:
      'Thời gian: 9:00, Thứ Bảy, ngày 28/12/2024. Địa điểm: Trung tâm Hội nghị Quốc gia, Hà Nội. Diễn giả chính: GS. Hồ Tú Bảo, chuyên gia hàng đầu về AI và Khoa học Dữ liệu. Đăng ký vé miễn phí tại website của chúng tôi.',
    relativeUser: {
      image: 'https://i.pravatar.cc/150?u=techevent-organizer',
      title: 'Tech Vietnam',
    },
    Likes: 10,
    Comment: 5,
    IsLike: true,
    IsBookmark: true,
    CreatedAt: '2025-06-12T16:00:00Z',
  },
];

const TabNews = () => {
  const navigation = useNavigation();
  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={DUMMY_NEWS}
        keyExtractor={item => item.Id}
        contentContainerStyle={styles.listContent}
        ItemSeparatorComponent={() => <View style={{height: 10}} />}
        renderItem={({item}: {item: NewsItem}) => <NewsCard item={item} />}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f4f4f8',
  },
  listContent: {
    paddingVertical: 16,
  },
});

export default TabNews;
