import {PayloadAction, createSlice} from '@reduxjs/toolkit';
import {Category} from '../models/category';
import {fetchCategories} from '../actions/categoryAction';

interface CategoryStoreState {
  data: Array<Category>;
  loading?: boolean;
}

export type {CategoryStoreState};

const initState: CategoryStoreState = {
  data: [],
  loading: false,
};

export const categorySlice = createSlice({
  name: 'category',
  initialState: initState,
  reducers: {
    setData: <K extends keyof CategoryStoreState>(
      state: CategoryStoreState,
      action: PayloadAction<{
        stateName: K;
        data: CategoryStoreState[K];
      }>,
    ) => {
      state[action.payload.stateName] = action.payload.data;
    },
    onFetching: (state: CategoryStoreState, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    onFetchDone: (state: CategoryStoreState) => {
      state.loading = false;
    },
  },
  extraReducers: builder => {
    builder.addCase(fetchCategories.pending, state => {
      state.loading = true;
    });
    builder.addCase(fetchCategories.fulfilled, (state, action) => {
      state.loading = false;
      state.data = action.payload;
    });
    builder.addCase(fetchCategories.rejected, state => {
      state.loading = false;
    });
  },
});

export const {setData, onFetching, onFetchDone} = categorySlice.actions;

export default categorySlice.reducer;
