import { Pressable } from "react-native";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";

export const CardNotificationSkeleton = () => {
    return (
        <Pressable style={{ paddingVertical: 16, paddingHorizontal: 16 }}>
            <SkeletonPlaceholder>
                <SkeletonPlaceholder.Item
                    flexDirection="row"
                    alignContent="center"
                    alignItems="center">
                    <SkeletonPlaceholder.Item height={40} width={40} borderRadius={20} />
                    <SkeletonPlaceholder.Item marginLeft={12} >
                        <SkeletonPlaceholder.Item height={16} width={200} />
                        <SkeletonPlaceholder.Item marginVertical={8} height={16} width={250} />
                        <SkeletonPlaceholder.Item height={16} width={40} />
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder.Item>
            </SkeletonPlaceholder>
        </Pressable>
    );
};
