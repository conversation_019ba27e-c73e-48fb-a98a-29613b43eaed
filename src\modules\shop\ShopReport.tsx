import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import HeaderShop from '../../components/shop/HeaderShop';
import NavigateShop from '../../components/shop/NavigateShop';
import ReviewItem from '../../components/shop/ReviewItem';
import { Title } from '../../Config/Contanst';
import Chart from '../../components/shop/ShopReport/Chart';

const ChartReport = () => {
    return (
        <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <HeaderShop />
            </View>
            <NavigateShop
                title={Title.Report}
            />
            <Chart />
        </View >
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        maxHeight: 120
    },
});

export default ChartReport;