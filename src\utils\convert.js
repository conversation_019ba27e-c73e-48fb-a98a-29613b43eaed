import { parseString } from 'react-native-xml2js';

class HTMLProcessor {
    // using it
    static processHTMLContent = (html) => {
        return new Promise((resolve, reject) => {
            // We can't use DOMParser in React Native, so we'll use a different approach
            // with XML parsing library

            // First, ensure we have valid XML by wrapping the content if needed
            const wrappedHtml = html.trim().startsWith('<') ? html : `<div>${html}</div>`;

            parseString(wrappedHtml, (err, result) => {
                if (err) {
                    // If parsing fails, try a simple regex approach as fallback
                    const textContent = HTMLProcessor.extractTextWithRegex(html);
                    resolve(`<Text>${textContent}</Text>`);
                    return;
                }

                try {
                    // Extract text content recursively from the parsed structure
                    let textContent = HTMLProcessor.extractTextFromParsedXML(result);

                    // Clean up the text content
                    textContent = textContent.trim().replace(/\s+/g, ' ');

                    if (!textContent) {
                        resolve('');
                    } else {
                        // In React Native we use <Text> components instead of <p>
                        resolve(`${textContent}`);
                    }
                } catch (error) {
                    reject(error);
                }
            });
        });
    };

    // Helper method to extract text from parsed XML structure
    static extractTextFromParsedXML = (node) => {
        if (!node) return '';

        // If it's a string, return it
        if (typeof node === 'string') return node;

        // If it's an array, process each item
        if (Array.isArray(node)) {
            return node.map(item => HTMLProcessor.extractTextFromParsedXML(item)).join(' ');
        }

        // If it's an object, it might be an XML element
        if (typeof node === 'object') {
            let text = '';

            // Skip script and style tags
            if (node.tagName && ['script', 'style', 'img'].includes(node.tagName.toLowerCase())) {
                return '';
            }

            // Process all properties
            for (const key in node) {
                // Skip attributes and other special properties
                if (key === '$' || key === 'tagName') continue;

                text += ' ' + HTMLProcessor.extractTextFromParsedXML(node[key]);
            }

            return text;
        }

        return '';
    };

    // Fallback method using regex to extract text
    static extractTextWithRegex = (html) => {
        // Remove script, style tags and their content
        let text = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, ' ');
        text = text.replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, ' ');

        // Remove img tags
        text = text.replace(/<img\b[^>]*>/gi, '');

        // Remove all other HTML tags but keep their content
        text = text.replace(/<[^>]*>/g, ' ');

        // Clean up whitespace
        text = text.replace(/\s+/g, ' ').trim();

        return text;
    };
}

export default HTMLProcessor;