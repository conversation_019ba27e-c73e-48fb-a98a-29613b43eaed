import { NewsEvent } from "../../../../redux/models/newsEvent";


export interface Tab {
  id: string;
  label: string;
  icon: string;
}

export const MOCK_EVENTS: NewsEvent[] = [
  {
    DateCreated: Date.now(),
    Id: 'ev1',
    Key: '',
    DateEnd: 0,
    DateStart: 0,
    Code: '',
    Title: '<PERSON>ễn đàn chia sẻ phát triển thương hiệu cá nhân trên môi trường số',
    Address: 'Villa 5, Foresa 4, Nam Từ Liêm,...',
    Img: 'https://images.unsplash.com/photo-1527529482837-4698179dc6ce?w=800&q=80',
    Description: '',
    Name: '',
    Content: '',
    Sort: 0,
    Status: 0,
    Type: 1,
  },
  {
    DateCreated: Date.now(),
    Id: 'ev2',
    Key: '',
    DateEnd: 0,
    DateStart: 0,
    Code: '',
    Title: 'Workshop Kỹ năng mềm cho lập tr<PERSON><PERSON> viên thế hệ mới',
    Address: '<PERSON><PERSON> nhà <PERSON>, <PERSON><PERSON> công nghệ cao <PERSON>',
    Img: 'https://images.unsplash.com/photo-1511578314322-379afb476865?w=800&q=80',
    Description: '',
    Name: '',
    Content: '',
    Sort: 0,
    Status: 0,
    Type: 1,
  },
  {
    DateCreated: Date.now(),
    Id: 'ev3',
    Key: '',
    DateEnd: 0,
    DateStart: 0,
    Code: '',
    Title: 'HỘI NGHỊ CÔNG TÁC TỔ CHỨC VÀ NHÂN SỰ NĂM 2025',
    Address: 'Khu du lịch FLC Sầm Sơn',
    Img: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=800&q=80',
    Description: '',
    Name: '',
    Content: '',
    Sort: 0,
    Status: 0,
    Type: 0,
  },
  {
    DateCreated: Date.now(),
    Id: 'ev4',
    Key: '',
    DateEnd: Date.now() + 10 * 24 * 60 * 60 * 1000 + 15 * 60 * 60 * 1000 + 30 * 60 * 1000,
    DateStart: Date.now() + 10 * 24 * 60 * 60 * 1000 + 15 * 60 * 60 * 1000 + 30 * 60 * 1000,
    Code: '',
    Title: 'HỘI NGHỊ CÔNG TÁC TỔ CHỨC VÀ NHÂN SỰ NĂM 2025',
    Address: 'Khu du lịch FLC Sầm Sơn',
    Img: 'https://images.unsplash.com/photo-1561489396-888724a1543d?w=800&q=80',
    Description: '',
    Name: '',
    Content: '',
    Sort: 0,
    Status: 0,
    Type: 2,
  },
  {
    DateCreated: Date.now(),
    Id: 'ev5',
    Key: '',
    DateEnd: Date.now() + 12 * 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000,
    DateStart: Date.now() + 12 * 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000,
    Code: '',
    Title: 'HỘI NGHỊ CÔNG TÁC TỔ CHỨC VÀ NHÂN SỰ NĂM 2025',
    Address: 'Khu du lịch FLC Sầm Sơn',
    Img: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=800&q=80',
    Description: '',
    Name: '',
    Content: '',
    Sort: 0,
    Status: 0,
    Type: 2,
  },
];

export const TABS_DATA: Tab[] = [
  {id: 'news', label: 'Tin mới', icon: '🔔'},
  {id: 'events', label: 'Tin sự kiện', icon: '📢'},
  {id: 'guide', label: 'Hướng dẫn', icon: '📖'},
]; 