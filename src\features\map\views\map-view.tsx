import React, {useEffect, useRef, useState} from 'react';
import MapView, {<PERSON><PERSON>, PROVIDER_GOOGLE} from 'react-native-maps';
import {
  Dimensions,
  FlatList,
  Linking,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import ScreenHeader from '../../../Screen/Layout/header';
import {useNavigation, useRoute} from '@react-navigation/native';
import {TypoSkin} from '../../../assets/skin/typography';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
  faAngleLeft,
  faMapLocation,
  faMapPin,
  faSearch,
} from '@fortawesome/free-solid-svg-icons';
import Geolocation from '@react-native-community/geolocation';
import {
  DialogSearchMap,
  showDialogMap,
} from '../local-component/search-map-dialog';
import {ColorThemes} from '../../../assets/skin/colors';
import {SvgXml} from 'react-native-svg';

export const FilledDirection = ({
  size,
  color,
  opacity = 1,
}: {
  size?: number;
  color?: string;
  opacity?: number;
}) => (
  <SvgXml
    xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_7881_9366)"><path d="M9.00037 9.99988C8.73516 9.99988 8.4808 10.1052 8.29327 10.2928C8.10573 10.4803 8.00037 10.7347 8.00037 10.9999V14.9999H10.0004V11.9999H13.0004V14.4999L16.5004 10.9999L13.0004 7.49988V9.99988H9.00037ZM12.7074 1.39288L22.6074 11.2929C22.7948 11.4804 22.9002 11.7347 22.9002 11.9999C22.9002 12.265 22.7948 12.5194 22.6074 12.7069L12.7074 22.6069C12.5198 22.7944 12.2655 22.8997 12.0004 22.8997C11.7352 22.8997 11.4809 22.7944 11.2934 22.6069L1.39337 12.7069C1.2059 12.5194 1.10059 12.265 1.10059 11.9999C1.10059 11.7347 1.2059 11.4804 1.39337 11.2929L11.2934 1.39288C11.4809 1.20541 11.7352 1.1001 12.0004 1.1001C12.2655 1.1001 12.5198 1.20541 12.7074 1.39288Z" fill="${
      color ?? '#667994'
    }"/></g><defs><clipPath id="clip0_7881_9366"><rect width="24" height="24" fill="${
      color ?? '#667994'
    }"/></clipPath></defs></svg>`}
    width={size ?? 16}
    height={size ?? 16}
  />
);

export default function MapViewIndex({props}: any) {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const dialogMapRef = useRef<any>(null);

  const widthScr: number = Dimensions.get('screen').width;

  const mapRef = useRef<any>(null);
  const [currentRegion, setcurrentRegion] = useState({
    latitude: 21.040531,
    longitude: 105.774083,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });

  useEffect(() => {
    if (route.params != null) {
      setcurrentRegion({
        latitude: route.params?.lat,
        longitude: route.params?.lon,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    } else {
      animatedCurrentRegion();
    }
  }, []);

  const animatedCurrentRegion = () => {
    Geolocation.getCurrentPosition(position => {
      setcurrentRegion({
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
      onChangeMapPicker({
        lat: position.coords.latitude,
        lng: position.coords.longitude,
      });
    });
  };

  const onChangeMapPicker = ({lat, lng}: {lat: number; lng: number}) => {
    mapRef.current.animateToRegion({
      latitude: lat,
      longitude: lng,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    });
  };

  return (
    <View style={styles.container}>
      <DialogSearchMap ref={dialogMapRef} />
      <ScreenHeader
        backIcon={
          <FontAwesomeIcon icon={faAngleLeft} size={20} color="#00204D99" />
        }
        onBack={() => {
          navigation.goBack();
        }}
        action={
          <TouchableOpacity
            style={{padding: 8, borderRadius: 100}}
            onPress={() => {
              showDialogMap({
                ref: dialogMapRef,
                title: 'Tìm kiếm địa chỉ',
                onSubmit: value => {
                  // console.log('====================================');
                  // console.log(value);
                  setcurrentRegion({
                    latitude: value.geometry.location.lat,
                    longitude: value.geometry.location.lng,
                    latitudeDelta: 0.01,
                    longitudeDelta: 0.01,
                  });
                  onChangeMapPicker(value.geometry.location);
                  // console.log('====================================');
                },
                suggestList: [],
              });
            }}>
            <FontAwesomeIcon icon={faSearch} size={20} color="#00204D99" />
          </TouchableOpacity>
        }
        style={{
          zIndex: 1,
          backgroundColor: ColorThemes.light.transparent,
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          alignItems: 'flex-start',
        }}
      />

      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={currentRegion}
        zoomEnabled={true}
        zoomControlEnabled={true}>
        <Marker
          title="Yor are here"
          description="This is a description"
          coordinate={currentRegion}
          onPress={() => {
            mapRef.current.animateToRegion(currentRegion);
            setcurrentRegion(currentRegion);
          }}
        />
      </MapView>
      <View style={{flex: 1}} />
      <TouchableOpacity
        onPress={() =>
          Linking.openURL(
            Platform.OS === 'ios'
              ? `maps://app?saddr=${currentRegion.latitude}+${currentRegion.longitude}&daddr=21.040531+105.774083`
              : 'google.navigation:q=100+101',
          )
        }
        style={{
          padding: 12,
          marginRight: 16,
          backgroundColor: ColorThemes.light.primary_main_color,
          borderRadius: 40,
        }}>
        <FilledDirection color={ColorThemes.light.white} size={28} />
      </TouchableOpacity>
      {/* <TouchableOpacity
                onPress={() => animatedCurrentRegion()}
                style={{
                    padding: 12,
                    marginRight: 16,
                    backgroundColor: ColorSkin.primary,
                    borderRadius: 40,
                }}>
                <FontAwesomeIcon icon={faMapPin} size={28} color={ColorSkin.white} />
            </TouchableOpacity> */}
      <View style={[styles.ViewContainer]}>
        <FlatList
          style={{
            paddingHorizontal: 16,
          }}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          keyExtractor={(_, index) => index.toString()}
          ItemSeparatorComponent={() => <View style={{width: 16}} />}
          data={[{}, {}, {}, {}]}
          renderItem={({item, index}) => (
            <TouchableOpacity
              key={`${index}`}
              style={[
                styles.viewCard,
                {
                  width: (widthScr - 40) * 0.68,
                },
              ]}
            />
          )}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    gap: 16,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  ViewContainer: {
    height: 255,
    paddingBottom: 16,
    backgroundColor: ColorThemes.light.transparent,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowOpacity: 1,
    elevation: 20,
    shadowRadius: 20,
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowColor: 'rgba(0, 0, 0, 0.03)',
  },
  viewCard: {
    backgroundColor: 'grey',
    borderRadius: 8,
  },

  searchContainer: {
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
});
