import React, {useState} from 'react';
import {
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {
  faRocket,
  faBullhorn,
  faBook,
  faDownload,
  faFire,
  faGear,
} from '@fortawesome/free-solid-svg-icons';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {ColorThemes} from '../../../assets/skin/colors';

export type TabId =
  | 'news'
  | 'news-event'
  | 'guides'
  | 'downloads'
  | 'trending'
  | 'settings';

interface Props {
  onChangeTab: (tabId: TabId) => void;
}

const TABS_DATA = [
  {
    id: 'news',
    label: 'Tin mới',
    icon: faRocket,
  },
  {
    id: 'news-event',
    label: 'Tin sự kiện',
    icon: faBullhorn,
  },
  {
    id: 'guides',
    label: 'Hướng dẫn',
    icon: faBook,
  },
  {
    id: 'downloads',
    label: 'T<PERSON><PERSON> về',
    icon: faDownload,
  },
  {
    id: 'trending',
    label: '<PERSON> hướng',
    icon: faFire,
  },
  {
    id: 'settings',
    label: 'Cài đặt',
    icon: faGear,
  },
];

const ScrollableTabs = ({onChangeTab}: Props) => {
  const [activeTabId, setActiveTabId] = useState(TABS_DATA[0].id);

  const handleTabPress = (tabId: TabId) => {
    setActiveTabId(tabId);
    onChangeTab(tabId);
  };

  const renderTabItem = ({item}: {item: any}) => {
    const isActive = item.id === activeTabId;

    const TabContent = () => (
      <>
        <FontAwesomeIcon
          icon={item.icon}
          size={16}
          color={isActive ? ColorThemes.light.primary_main_color : '#333'}
          style={styles.tabIcon}
        />
        <Text
          style={[
            styles.tabLabel,
            isActive && {color: ColorThemes.light.primary_main_color},
          ]}>
          {item.label}
        </Text>
      </>
    );

    return (
      <TouchableOpacity onPress={() => handleTabPress(item.id)}>
        {isActive ? (
          <LinearGradient
            colors={['#90C8FB', '#8DC4F7E5', '#B6F5FE']}
            start={{x: 0, y: 0.5}}
            end={{x: 1, y: 0.5}}
            style={[styles.tabItem]}>
            <TabContent />
          </LinearGradient>
        ) : (
          <View style={styles.tabItem}>
            <TabContent />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView>
      <FlatList
        data={TABS_DATA}
        renderItem={renderTabItem}
        keyExtractor={item => item.id}
        horizontal={true} // Quan trọng: để list cuộn ngang
        showsHorizontalScrollIndicator={false} // Ẩn thanh cuộn ngang cho giao diện sạch hơn
        contentContainerStyle={styles.listContentContainer}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  listContentContainer: {
    paddingHorizontal: 10,
    paddingVertical: 10,
  },
  tabItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 10,
    marginHorizontal: 5,
    borderRadius: 10,
  },
  tabIcon: {
    marginRight: 8,
  },
  tabLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333', // Màu chữ mặc định
  },
  activeTabLabel: {
    color: '#007bff', // Màu chữ khi được chọn
  },
});

export default ScrollableTabs;
