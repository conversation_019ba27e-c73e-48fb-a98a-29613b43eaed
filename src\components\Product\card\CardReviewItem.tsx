import React, { use, useEffect, useState } from 'react';
import {
  View,
  Image,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
} from 'react-native';
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import { Winicon } from 'wini-mobile-components';
import { TypeMenuReview } from '../../../Config/Contanst';
import ReviewInfo from '../ReviewInfo';
import { TypoSkin } from '../../../assets/skin/typography';

const ReviewProductIteCard = (
  { item, index }: { item: any; index: number },
  type: string,
) => {
  return (
    <Pressable style={styles.wrapper} key={`key ${index}`}>
      <View style={styles.review}>
        <ReviewInfo item={item} index={index} />
        <View style={styles.reviewContent}>
          <Text style={styles.description}>{item.description}</Text>
          <ScrollView style={styles.imagesProduct} horizontal={true}>
            {item?.imagesProduct?.map((image: string, index: number) => (
              <Image
                key={`item-${index}`}
                source={{ uri: image }}
                style={styles.avatarProduct}
              />
            ))}
          </ScrollView>
          {type == TypeMenuReview.Product ? (
            <View style={styles.reviewDetail}>
              <View style={styles.imageDetail}>
                <Image
                  source={{
                    uri: item?.product?.productImage,
                  }}
                  style={styles.avatarDetail}
                />
              </View>
              <View style={{ justifyContent: 'center', marginLeft: 11 }}>
                <Text style={styles.tag}>{item?.product?.productName}</Text>
                <Text style={styles.size}>{item?.product?.property}</Text>
                <Text style={styles.size}>{item?.product?.Refund}</Text>
              </View>
            </View>
          ) : (
            <View style={styles.reviewDetail}>
              <View style={styles.orderDetail}>
                <Winicon src="fill/shopping/a-chart" size={20} color="blue" />
                <Text style={styles.orderName}>{item?.Order?.orderId}</Text>
              </View>
            </View>
          )}
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    marginTop: 26,
    marginRight: 20,
    marginLeft: 25,
  },

  review: {
    flexDirection: 'column',
    borderBottomWidth: 0.2,
    borderBottomColor: '#00FFFF',
    width: '100%',
  },

  reviewContent: {
    marginLeft: '19%',
  },

  description: {
    ...TypoSkin.body3,
    marginVertical: 5,
  },
  imagesProduct: {
    flexDirection: 'row',
    marginTop: 10,
    width: '100%',
    height: 100,
  },
  images: {
    flexDirection: 'row',
    marginTop: 10,
  },
  productImage: {
    width: 100,
    height: 100,
    marginRight: 10,
    borderRadius: 5,
  },
  reviewDetail: {
    flexDirection: 'row',
    alignContent: 'center',
    paddingBottom: 13
  },
  avatarProduct: {
    width: 80,
    height: 80,
    borderRadius: 20,
    marginRight: 10,
  },
  imageDetail: {
    borderWidth: 5,
    borderRadius: 50,
    width: 50,
    height: 50,
    marginRight: 10,
    borderColor: '#F8F8FF',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  avatarDetail: {
    width: 40,
    height: 40,
    borderRadius: 50,
  },
  tag: {
    ...TypoSkin.title4,
    fontWeight: '700',
    color: '#555',
  },
  size: {
    fontSize: 7,
    color: '#888',
  },
  orderDetail: {
    flexDirection: 'row',
    margin: 10,
  },
  orderName: {
    fontSize: 20,
    color: 'blue',
    marginLeft: 5,
  },
});

export default ReviewProductIteCard;
