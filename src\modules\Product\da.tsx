import { DataController } from "../../base/baseController";

export class ProductDA {
    private ShopController: DataController;
    constructor() {
        this.ShopController = new DataController('Product');
    }

    async getProducts(ShopId: string, Status?: number) {
        const response = await this.ShopController.aggregateList(
            {
                searchRaw: `@ShopId:{${ShopId}} @Status: [${Status}]`,
            },
        );
        if (response?.code === 200) {
            return response;
        }
        return null;
    }

    async getAllProducts(ShopId: string) {
        const response = await this.ShopController.aggregateList(
            {
                searchRaw: `@ShopId:{${ShopId}}`,
            },
        );
        if (response?.code === 200) {
            return response;
        }
        return null;
    }

}

export default ProductDA;