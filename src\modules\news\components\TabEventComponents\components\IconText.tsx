import React, {FC} from 'react';
import {Image, ImageSourcePropType, StyleSheet, Text, View} from 'react-native';
import {ColorThemes} from '../../../../../assets/skin/colors';

interface IconTextProps {
  icon?: ImageSourcePropType;
  text: string;
}

export const IconText: FC<IconTextProps> = ({icon, text, icon: Icon}) => (
  <View style={styles.iconTextContainer}>
    {icon && <Image source={icon} style={{width: 16, height: 16}} />}
    <Text style={styles.iconTextText} numberOfLines={1}>
      {text}
    </Text>
  </View>
);

const styles = StyleSheet.create({
  iconTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  iconTextText: {
    fontSize: 14,
    color: ColorThemes.light.infor_text_color,
    flexShrink: 1,
    marginLeft: 4,
  },
});
