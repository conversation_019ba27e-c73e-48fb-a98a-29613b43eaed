import {View} from 'react-native';
import ScreenHeader from '../../Screen/Layout/header';
import React, {useCallback, useState} from 'react';
import ScrollableTabs, {TabId} from './scrollable/ScrollableTabs';
import TabNews from './components/TabNews';
import TabEvents from './components/TabEvents';

const NewsScreen = () => {
  const [tab, setTab] = useState<TabId>('news');
  const onChangeTab = useCallback((tab: TabId) => {
    console.log('onChangeTab', tab);
    setTab(tab);
  }, []);
  return (
    <View style={{flex: 1}}>
      <ScreenHeader title={'Tin tức'} />

      <View style={{flex: 1}}>
        <ScrollableTabs onChangeTab={onChangeTab} />

        {tab === 'news' && <TabNews />}
        {tab === 'news-event' && <TabEvents />}
      </View>
    </View>
  );
};

export default NewsScreen;
