import React from 'react';
import {StyleSheet, TextStyle, View} from 'react-native';

export default function WScreenFooter({
  children,
  style,
}: {
  children: React.ReactNode;
  style?: TextStyle;
}) {
  return <View style={[styles.container, style ?? {}]}>{children}</View>;
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingTop: 12,
    paddingBottom: 32,
    shadowOpacity: 1,
    elevation: 20,
    shadowRadius: 20,
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowColor: 'rgba(0, 0, 0, 0.03)',
  },
});
