import 'react-native-reanimated';
/**
 * @format
 */

import { AppRegistry } from 'react-native';
import App from './App';
import { name as appName } from './app.json';

import 'react-native-get-random-values'
import { Text, TextInput } from 'react-native';

AppRegistry.registerComponent(appName, () => App);

//ADD this disable scale fontsize with physic device
if (Text.defaultProps == null) {
    Text.defaultProps = {};
    Text.defaultProps.allowFontScaling = false;
    Text.defaultProps.Color = '#111010FF';
}

if (TextInput.defaultProps == null) {
    TextInput.defaultProps = {};
    TextInput.defaultProps.allowFontScaling = false;
}
