import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { ComponentStatus, showSnackbar } from 'wini-mobile-components';
import GiftDA from '../modules/gift/giftDA';

// Test component để kiểm tra chức năng đổi quà
const GiftExchangeTest = () => {
  const giftDA = new GiftDA();

  // Test lấy danh sách quà tặng
  const testGetGifts = async () => {
    try {
      console.log('Testing getGifts...');
      const result = await giftDA.getGifts(1, 10);
      console.log('getGifts result:', result);
      
      showSnackbar({
        message: `<PERSON><PERSON>y danh sách quà tặng thành công: ${result?.data?.length || 0} items`,
        status: ComponentStatus.SUCCSESS,
      });
    } catch (error) {
      console.error('Error testing getGifts:', error);
      showSnackbar({
        message: 'Lỗi khi test getGifts',
        status: ComponentStatus.ERROR,
      });
    }
  };

  // Test lấy điểm hiện tại
  const testGetCurrentPoints = async () => {
    try {
      console.log('Testing getCurrentPoints...');
      // Sử dụng customer ID mẫu
      const testCustomerId = 'test-customer-id';
      const points = await giftDA.getCurrentPoints(testCustomerId);
      console.log('getCurrentPoints result:', points);
      
      showSnackbar({
        message: `Điểm hiện tại: ${points}`,
        status: ComponentStatus.SUCCSESS,
      });
    } catch (error) {
      console.error('Error testing getCurrentPoints:', error);
      showSnackbar({
        message: 'Lỗi khi test getCurrentPoints',
        status: ComponentStatus.ERROR,
      });
    }
  };

  // Test đổi quà
  const testExchangeGift = async () => {
    try {
      console.log('Testing exchangeGift...');
      
      Alert.alert(
        'Test Exchange Gift',
        'Bạn có muốn test chức năng đổi quà không?',
        [
          { text: 'Hủy', style: 'cancel' },
          {
            text: 'Đồng ý',
            onPress: async () => {
              try {
                // Sử dụng dữ liệu mẫu
                const testGiftId = 'test-gift-id';
                const testCustomerId = 'test-customer-id';
                const testPoints = 100;
                
                const result = await giftDA.exchangeGift(testGiftId, testCustomerId, testPoints);
                console.log('exchangeGift result:', result);
                
                if (result?.code === 200) {
                  showSnackbar({
                    message: 'Test đổi quà thành công!',
                    status: ComponentStatus.SUCCSESS,
                  });
                } else {
                  showSnackbar({
                    message: 'Test đổi quà thất bại',
                    status: ComponentStatus.ERROR,
                  });
                }
              } catch (error) {
                console.error('Error in exchange test:', error);
                showSnackbar({
                  message: 'Lỗi khi test exchangeGift',
                  status: ComponentStatus.ERROR,
                });
              }
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error testing exchangeGift:', error);
    }
  };

  // Test lấy danh sách chờ duyệt
  const testGetPendingExchanges = async () => {
    try {
      console.log('Testing getPendingExchanges...');
      const testCustomerId = 'test-customer-id';
      const result = await giftDA.getExchangeHistory(testCustomerId, 1, 10, 0); // Status = 0 for pending
      console.log('getPendingExchanges result:', result);

      showSnackbar({
        message: `Chờ duyệt: ${result?.data?.length || 0} items`,
        status: ComponentStatus.SUCCSESS,
      });
    } catch (error) {
      console.error('Error testing getPendingExchanges:', error);
      showSnackbar({
        message: 'Lỗi khi test getPendingExchanges',
        status: ComponentStatus.ERROR,
      });
    }
  };

  // Test lấy lịch sử đổi quà
  const testGetExchangeHistory = async () => {
    try {
      console.log('Testing getExchangeHistory...');
      const testCustomerId = 'test-customer-id';
      const result = await giftDA.getExchangeHistory(testCustomerId, 1, 10);
      console.log('getExchangeHistory result:', result);

      showSnackbar({
        message: `Lịch sử đổi quà: ${result?.data?.length || 0} items`,
        status: ComponentStatus.SUCCSESS,
      });
    } catch (error) {
      console.error('Error testing getExchangeHistory:', error);
      showSnackbar({
        message: 'Lỗi khi test getExchangeHistory',
        status: ComponentStatus.ERROR,
      });
    }
  };

  // Test lấy categories
  const testGetGiftCategories = async () => {
    try {
      console.log('Testing getGiftCategories...');
      const result = await giftDA.getGiftCategories();
      console.log('getGiftCategories result:', result);
      
      showSnackbar({
        message: `Categories: ${result?.length || 0} items`,
        status: ComponentStatus.SUCCSESS,
      });
    } catch (error) {
      console.error('Error testing getGiftCategories:', error);
      showSnackbar({
        message: 'Lỗi khi test getGiftCategories',
        status: ComponentStatus.ERROR,
      });
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Gift Exchange Test</Text>
      
      <TouchableOpacity style={styles.button} onPress={testGetGifts}>
        <Text style={styles.buttonText}>Test Get Gifts</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testGetCurrentPoints}>
        <Text style={styles.buttonText}>Test Get Current Points</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testExchangeGift}>
        <Text style={styles.buttonText}>Test Exchange Gift</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testGetPendingExchanges}>
        <Text style={styles.buttonText}>Test Get Pending Exchanges</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testGetExchangeHistory}>
        <Text style={styles.buttonText}>Test Get Exchange History</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testGetGiftCategories}>
        <Text style={styles.buttonText}>Test Get Gift Categories</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  button: {
    backgroundColor: '#4169E1',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default GiftExchangeTest;
