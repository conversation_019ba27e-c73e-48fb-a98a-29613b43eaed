import {format, parseISO} from 'date-fns';
import {View, Image, Text, StyleSheet} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {NotificationItem} from '../../../../redux/models/notification';

const NOTIFICATION_CONFIG = {
  1: {
    image: require('../../../../assets/images/noti_general_icon.png'),
  },
  2: {
    image: require('../../../../assets/images/noti_in_icon.png'),
  },
  3: {
    image: require('../../../../assets/images/noti_out_icon.png'),
  },
};

interface NotificationItemProps {
  item: NotificationItem;
}

const NotificationCard = ({item}: NotificationItemProps) => {
  // const config = NOTIFICATION_CONFIG[item.Type] || NOTIFICATION_CONFIG.general;
  // const parsedDate = parseISO(item.DateCreated);
  // const formattedTime = format(parsedDate, 'HH:mm - dd/MM/yyyy');

  return (
    <View
      style={[
        styles.itemContainer,
        {backgroundColor: ColorThemes.light.primary_background},
      ]}>
      <View style={[styles.iconContainer]}>
        {/* <Image source={config.image} /> */}
      </View>
      <View style={styles.contentContainer}>
        <Text style={styles.text}>{item.Content}</Text>
        {/* <Text style={styles.timestamp}>{formattedTime}</Text> */}
      </View>
      {item.Status === 0 && <View style={styles.unreadDot} />}
    </View>
  );
};

const styles = StyleSheet.create({
  listContainer: {
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: '600',
    color: '#8A8A8E',
    marginBottom: 12,
    marginTop: 20,
  },
  itemContainer: {
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  iconContainer: {
    width: 30,
    height: '100%',
    borderRadius: 8,
    marginRight: 12,
  },
  // Style cho emoji
  emoji: {
    fontSize: 20, // Chỉnh kích thước emoji tại đây
  },
  contentContainer: {
    flex: 1,
  },
  text: {
    fontSize: 15,
    color: '#0D1C2E',
    lineHeight: 22,
    fontWeight: '500',
  },
  timestamp: {
    fontSize: 13,
    color: '#6c757d',
    marginTop: 4,
  },
  unreadDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#FF3B30',
    marginLeft: 10,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#8A8A8E',
  },
});

export default NotificationCard;
