import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Platform,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {useNavigation} from '@react-navigation/native';
import Svg, {Path} from 'react-native-svg';
import {TypoSkin} from '../assets/skin/typography';

const {width} = Dimensions.get('window');
const STATUSBAR_HEIGHT = StatusBar.currentHeight || 0;

interface CartHeaderProps {
  title: string;
  onBack?: () => void;
  rightComponent?: React.ReactNode;
  backgroundColor?: string;
  waveColor?: string;
  textColor?: string;
}

const CartHeader: React.FC<CartHeaderProps> = ({
  title,
  onBack,
  rightComponent,
  backgroundColor = '#2962FF', // Màu xanh dương như trong hình
  waveColor = '#8CDBF4', // <PERSON><PERSON>u xanh nhạt cho đường cong
  textColor = '#FFFFFF', // <PERSON><PERSON>u trắng cho chữ
}) => {
  const navigation = useNavigation();

  const handleBackPress = () => {
    if (onBack) {
      onBack();
    } else {
      navigation.goBack();
    }
  };

  // Thiết lập status bar để phù hợp với header
  React.useEffect(() => {
    StatusBar.setBarStyle('light-content');
    if (Platform.OS === 'android') {
      StatusBar.setBackgroundColor(backgroundColor);
      StatusBar.setTranslucent(true);
    }

    return () => {
      // Reset status bar khi component unmount
      StatusBar.setBarStyle('default');
      if (Platform.OS === 'android') {
        StatusBar.setBackgroundColor('transparent');
      }
    };
  }, [backgroundColor]);

  return (
    <View style={[styles.container, {backgroundColor}]}>
      {/* Khoảng trống cho status bar */}
      <SafeAreaView style={{backgroundColor}}>
        <View style={styles.statusBarPlaceholder} />
      </SafeAreaView>

      {/* Hiệu ứng đường cong ở dưới */}
      <View style={styles.waveContainer}>
        <Svg height="80" width={width} viewBox={`0 0 ${width} 80`}>
          <Path
            d={`
              M0 0
              L${width} 0
              L${width} 40
              C${width * 0.75} 60, ${width * 0.25} 60, 0 40
              Z
            `}
            fill={waveColor}
          />
        </Svg>
      </View>

      {/* Nội dung header */}
      <View style={styles.headerContent}>
        <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
          <Winicon
            src="outline/arrows/left-arrow"
            size={24}
            color={textColor}
          />
        </TouchableOpacity>

        <Text style={[styles.title, {color: textColor}]}>{title}</Text>

        {rightComponent && (
          <View style={styles.rightComponent}>{rightComponent}</View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 150, // Điều chỉnh dựa trên thiết kế của bạn
    position: 'relative',
  },
  statusBarPlaceholder: {
    height: Platform.OS === 'ios' ? 0 : STATUSBAR_HEIGHT,
  },
  waveContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    height: 56,
    position: 'relative',
    zIndex: 2,
  },
  backButton: {
    position: 'absolute',
    left: 16,
    padding: 8,
  },
  title: {
    ...TypoSkin.heading5,
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
  },
  rightComponent: {
    position: 'absolute',
    right: 16,
  },
});

export default CartHeader;
