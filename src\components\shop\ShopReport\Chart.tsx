import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import { ScrollView } from 'react-native-gesture-handler';
import ChartMoney from './ChartMakeMoney';
import { ChartType } from '../../../Config/Contanst';
import ChartProduct from './ChartProduct';
import ChartOrder from './ChartOrder';

const { width } = Dimensions.get('window');

const Chart = () => {
    const [SelectChart, setSelectChart] = useState(ChartType.MakeMoney);

    return (
        <View style={styles.container}>
            <ScrollView style={styles.tabContainer} horizontal={true}>
                <TouchableOpacity style={SelectChart == ChartType.MakeMoney ? styles.tabAction : styles.tab} onPress={() => setSelectChart(ChartType.MakeMoney)}>
                    <Text style={SelectChart == ChartType.MakeMoney ? styles.tabTextAction : styles.tabText}>Báo cáo doanh thu</Text>
                </TouchableOpacity>
                <TouchableOpacity style={SelectChart == ChartType.ProductData ? styles.tabAction : styles.tab} onPress={() => setSelectChart(ChartType.ProductData)}>
                    <Text style={SelectChart == ChartType.ProductData ? styles.tabTextAction : styles.tabText}>Báo cáo sản phẩm</Text>
                </TouchableOpacity>
                <TouchableOpacity style={SelectChart == ChartType.OrderData ? styles.tabAction : styles.tab} onPress={() => setSelectChart(ChartType.OrderData)}>
                    <Text style={SelectChart == ChartType.ProductData ? styles.tabTextAction : styles.tabText}>Báo cáo đơn hàng</Text>
                </TouchableOpacity>
            </ScrollView>
            {SelectChart === ChartType.MakeMoney && <ChartMoney />}
            {SelectChart === ChartType.ProductData && <ChartProduct />}
            {SelectChart === ChartType.OrderData && <ChartOrder />}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        padding: 10,
        backgroundColor: '#fff',
        borderRadius: 8,
        margin: 10,
    },
    tabContainer: {
        flexDirection: 'row',
        marginBottom: 10,
    },
    tab: {
        padding: 4,
        borderBottomWidth: 2,
        borderBottomColor: 'transparent',
        marginLeft: 5,
        marginRight: 5,
        fontFamily: 'Roboto'
    },
    tabAction: {
        padding: 4,
        borderBottomWidth: 2,
        borderBottomColor: 'transparent',
        marginLeft: 5,
        marginRight: 5,
        backgroundColor: '#87CEFA',
        borderRadius: 10,
        fontFamily: 'Roboto'
    },
    tabTextAction: {
        fontSize: 20,
        color: 'blue',
        padding: 3
    },
    tabText: {
        fontSize: 20,
        color: '#696969',
        padding: 3
    },
});

export default Chart;