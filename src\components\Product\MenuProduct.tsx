import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { View, Text, Image, TouchableOpacity, Switch, StyleSheet } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { Winicon } from 'wini-mobile-components';
import { RootScreen } from '../../router/router';
import { Title, TypeMenuPorduct } from '../../Config/Contanst';
import { red } from 'react-native-reanimated/lib/typescript/Colors';

interface MenuProductProps {
    menu: string;
    setMenu: (menu: string) => void;
    data: any[]
}
const MenuProduct = (props: MenuProductProps) => {
    const { menu, setMenu, data } = props;
    const TypeMenuPorduct = ["Còn hàng", "Hết hàng", "Chờ duyệt", "Vi phạm", "Ẩn"]
    return (
        <View style={styles.header}>
            {data && data.length > 0 && data.map((item, index) => (
                < TouchableOpacity style={styles.tab} onPress={() => setMenu(item.name)}>
                    <Text style={menu == TypeMenuPorduct[index] ? styles.tabTextAction : styles.tabText}>{item.name}</Text>
                    <Text style={menu == TypeMenuPorduct[index] ? styles.tabTextAction : styles.tabText}>({item.number})</Text>
                </TouchableOpacity>
            ))}
        </View>
    );
};

const styles = StyleSheet.create({
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: 10,
    },
    tab: {
        padding: 5,
    },
    tabAction: {
        padding: 5,
    },
    tabText: {
        fontSize: 14,
        margin: "auto",
        color: "#C0C0C0"
    },
    tabTextAction: {
        fontSize: 14,
        margin: "auto",
        color: "DimGray"
    },
});

export default MenuProduct;