import AsyncStorage from '@react-native-async-storage/async-storage';

export const saveObjToAsyncStorage = async (key, value) => {
  try {
    const jsonValue = JSON.stringify(value);
    await AsyncStorage.setItem(key, jsonValue);
  } catch (e) {
    console.error('Lỗi khi lưu data:', error);
  }
};

export const getObjToAsyncStorage = async key => {
  try {
    const jsonValue = await AsyncStorage.getItem(key);
    return jsonValue != null ? JSON.parse(jsonValue) : null;
  } catch (e) {
    console.error('Lỗi khi lấy data:', error);
    return null;
  }
};

export const saveDataToAsyncStorage = async (key, value) => {
  try {
    await AsyncStorage.setItem(key, value);
  } catch (error) {
    console.error('Lỗi khi lưu data:', error);
  }
};

export const getDataToAsyncStorage = async key => {
  try {
    const data = await AsyncStorage.getItem(key);
    return data;
  } catch (error) {
    console.error('Lỗi khi lấy data:', error);
    return null;
  }
};

export const removeDataToAsyncStorage = async key => {
  try {
    const data = await AsyncStorage.removeItem(key);
    return data;
  } catch (error) {
    console.error('Lỗi khi xóa data:', error);
    return null;
  }
};

export const clearDataToAsyncStorage = async () => {
  try {
    const data = await AsyncStorage.clear();
    return data;
  } catch (error) {
    console.error('Lỗi clear data:', error);
    return null;
  }
};