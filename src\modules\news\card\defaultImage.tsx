/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {
  Dimensions,
  Image,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import { App<PERSON>utton, Winicon } from 'wini-mobile-components';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { ColorThemes } from '../../../assets/skin/colors';
import { TypoSkin } from '../../../assets/skin/typography';
import ConfigAPI from '../../../Config/ConfigAPI';
interface cardItem {
  Id: string;
  Name: string;
  Description: string;
  Price: number;
  Img: string;
  Content: string;
  relativeUser?: { image?: string; title?: string };
}

interface Props {
  containerStyle?: ViewStyle;
  flexDirection?: 'default' | 'row';
  imgStyle?: ViewStyle;
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  data: cardItem;
  listItems?: Array<any>;
  listTags?: Array<any>;
  onPressSeeMore?: () => void;
  onPressDetail?: () => void;
  onPressLikeAction?: () => void;
  subtitleView?: React.ReactNode;
  reportContent?: React.ReactNode;
  actionView?: React.ReactNode;
  showContent?: boolean;
}

export function DefaultWithImage(props: Props) {
  return (
    <TouchableOpacity
      onPress={props.onPressDetail}
      disabled={props.onPressDetail ? false : true}
      style={[
        {
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
          alignSelf: 'flex-start',
          flexDirection: props.flexDirection === 'row' ? 'row' : 'column',
          width: Dimensions.get('screen').width - 48,
          flex: 1,
          ...props.containerStyle,
        },
      ]}>
      <View
        style={[
          stylesDefault.mainContainer,
          {
            flexDirection: props.flexDirection === 'row' ? 'row' : 'column',
          },
        ]}>
        <View
          style={[
            stylesDefault.img,
            {
              height: props.flexDirection === 'row' ? 64 : 234,
              alignSelf: 'flex-start',
              width:
                props.flexDirection === 'row'
                  ? 64
                  : Dimensions.get('screen').width - 48,
              borderColor: ColorThemes.light.neutral_main_border_color,
              borderWidth: 1,
              ...props.imgStyle,
            },
          ]}>
          <Image
            source={{
              uri: props?.data.Img
                ? `${ConfigAPI.urlImg + props?.data.Img}`
                : 'https://www.figma.com/file/QeG7fLsM5o0Oje9Wagi1xc/image/be9e79d2b2cc1e79b9b9d50cba88c6febddd5d7f',
            }}
            style={{
              width: '100%',
              height: '100%',
              borderRadius: 8,
              objectFit: 'cover',
              position: 'absolute',
            }}
            resizeMode="cover"
          />
        </View>
        <View
          style={{
            borderBottomColor: ColorThemes.light.neutral_main_border_color,
            borderBottomWidth: 1,
            paddingBottom: 8,
            flexDirection: props.flexDirection === 'row' ? 'row' : 'column',
            alignSelf: 'flex-start',
            maxWidth: props.flexDirection === 'row' ? '80%' : '100%',
          }}>
          <View style={[stylesDefault.mainContent]}>
            {/* infor on top */}
            {props.data.relativeUser ? (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'flex-start',
                  gap: 4,
                  marginBottom: 4,
                }}>
                {/* infor img */}
                <View
                  style={[
                    {
                      width: 20,
                      height: 20,
                      borderRadius: 100,
                      backgroundColor: 'black',
                    },
                  ]}>
                  <Image
                    source={{
                      uri: props?.data.Img
                        ? `${ConfigAPI.urlImg + props?.data.Img}`
                        : 'https://reactnative.dev/img/tiny_logo.png',
                    }}
                    style={{ width: '100%', height: '100%', borderRadius: 100 }}
                  />
                </View>
                {/* infor text */}
                <View style={{ flex: 1 }}>
                  <Text style={[stylesDefault.inforTitle]}>
                    {props.data.relativeUser.title ?? ''}
                  </Text>
                </View>
              </View>
            ) : null}
            {/* title */}
            {props.data.Name && typeof props.data.Name === 'string' ? (
              <Text
                style={[
                  stylesDefault.titleStyle,
                  {
                    paddingBottom: props.data.Description ? 4 : 0,
                    ...props.titleStyle,
                  },
                ]}
                numberOfLines={2}>
                {props.data.Name ?? ''}
              </Text>
            ) : null}
            {/* subtitle */}
            {props.subtitleView ? (
              props.subtitleView
            ) : props.data.Description ? (
              <View style={{ paddingTop: 4, paddingBottom: 8 }}>
                <Text
                  style={[stylesDefault.subTitleStyle, props.subtitleStyle]}
                  numberOfLines={4}>
                  {props.data.Description ?? ''}
                </Text>
              </View>
            ) : null}
            {props.data.Content && props.showContent ? (
              <View style={{}}>
                <Text
                  style={[stylesDefault.bodyContentStyle]}
                  numberOfLines={4}>
                  {props.data.Content ?? ''}
                </Text>
              </View>
            ) : null}
            {props.listItems?.length ? (
              <View style={{ width: '100%', paddingTop: 16 }}>
                {props.listItems.map((item, index) => {
                  return (
                    <View
                      key={index}
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        paddingVertical: 4,
                        gap: 8,
                      }}>
                      <View
                        style={[
                          {
                            width: 16,
                            borderRadius: 100,
                          },
                        ]}>
                        {item.icon ? (
                          <Winicon src={item.icon} size={16} />
                        ) : (
                          <Text style={[stylesDefault.inforTitle]}>*</Text>
                        )}
                      </View>
                      <Text
                        style={[stylesDefault.inforTitle, { color: '#313135' }]}>
                        {item.title}
                      </Text>
                    </View>
                  );
                })}
              </View>
            ) : null}
            {props.onPressSeeMore && props.listItems?.length ? (
              <AppButton
                title={'See more'}
                containerStyle={{
                  justifyContent: 'flex-start',
                  alignSelf: 'baseline',
                  marginVertical: 8,
                }}
                backgroundColor={'transparent'}
                textStyle={TypoSkin.buttonText3}
                borderColor="transparent"
                suffixIconSize={16}
                suffixIcon={'outline/arrows/circle-arrow-right'}
                onPress={props.onPressSeeMore}
                textColor={ColorThemes.light.infor_main_color}
              />
            ) : null}
            {props.listTags?.length ? (
              <View style={{ width: '100%', flexDirection: 'row', gap: 8 }}>
                {props.listTags.map((item, index) => {
                  return (
                    <View
                      key={index}
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        backgroundColor:
                          ColorThemes.light.neutral_main_background_color,
                        paddingHorizontal: 8,
                        borderRadius: 24,
                        borderColor:
                          ColorThemes.light.neutral_bolder_border_color,
                        borderWidth: 1,
                        paddingVertical: 4,
                        gap: 4,
                      }}>
                      <Text style={[stylesDefault.inforTitle]}>
                        {item.title}
                      </Text>
                      <Winicon src={'outline/arrows/right-arrow'} size={12} />
                    </View>
                  );
                })}
              </View>
            ) : null}
            {props.flexDirection === 'default' && props.reportContent && (
              <View
                style={{
                  height: 96,
                  backgroundColor: '#f0f0f0',
                  borderRadius: 8,
                  marginVertical: 16,
                  padding: 24,
                  justifyContent: 'space-between',
                  flexDirection: 'row',
                }}>
                <View style={{ gap: 4 }}>
                  {props.reportContent ? props.reportContent : null}
                </View>
                <View style={{ gap: 4 }}>
                  <Text
                    style={{
                      ...TypoSkin.heading6,
                      color: ColorThemes.light.neutral_text_title_color,
                    }}>
                    975k+
                  </Text>
                  <Text
                    style={{
                      ...TypoSkin.subtitle4,
                      color: ColorThemes.light.neutral_text_title_color,
                    }}>
                    Time on desgin
                  </Text>
                </View>
                <View style={{ gap: 4 }}>
                  <Text
                    style={{
                      ...TypoSkin.heading6,
                      color: ColorThemes.light.neutral_text_title_color,
                    }}>
                    60k+
                  </Text>
                  <Text
                    style={{
                      ...TypoSkin.subtitle4,
                      color: ColorThemes.light.neutral_text_title_color,
                    }}>
                    Average 5-Star
                  </Text>
                </View>
              </View>
            )}
            <View
              style={{
                flex: 1,
                flexDirection: 'row',
                width: '100%',
                alignItems: 'center',
              }}>
              {props.flexDirection === 'default' && props.actionView && (
                <View
                  style={{ flex: 1, flexDirection: 'row', alignItems: 'center' }}>
                  {props.actionView ? props.actionView : null}
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      gap: 4,
                      justifyContent: 'flex-start',
                      alignItems: 'center',
                    }}>
                    <AppButton
                      backgroundColor={'transparent'}
                      borderColor="transparent"
                      onPress={() => { }}
                      containerStyle={{ padding: 4 }}
                      title={
                        <Winicon src="outline/layout/circle-plus" size={20} />
                      }
                    />
                    <AppButton
                      backgroundColor={'transparent'}
                      borderColor="transparent"
                      onPress={() => { }}
                      containerStyle={{ padding: 4 }}
                      title={
                        <Winicon
                          src="outline/user interface/i-edit"
                          size={20}
                        />
                      }
                    />
                    <AppButton
                      backgroundColor={'transparent'}
                      borderColor="transparent"
                      onPress={() => { }}
                      containerStyle={{ padding: 4 }}
                      title={
                        <Winicon
                          src="outline/development/refresh-02"
                          size={18}
                        />
                      }
                    />
                  </View>
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      justifyContent: 'flex-end',
                      alignItems: 'center',
                      gap: 8,
                    }}>
                    <AppButton
                      backgroundColor={
                        ColorThemes.light.neutral_main_background_color
                      }
                      borderColor="transparent"
                      onPress={() => { }}
                      textStyle={TypoSkin.buttonText3}
                      textColor={ColorThemes.light.neutral_text_subtitle_color}
                      containerStyle={{
                        paddingHorizontal: 12,
                        height: 32,
                        borderRadius: 8,
                      }}
                      title={'Button'}
                    />
                    <AppButton
                      backgroundColor={ColorThemes.light.primary_main_color}
                      borderColor="transparent"
                      onPress={() => { }}
                      textStyle={TypoSkin.buttonText3}
                      textColor={
                        ColorThemes.light.neutral_absolute_background_color
                      }
                      containerStyle={{
                        paddingHorizontal: 12,
                        height: 32,
                        borderRadius: 8,
                      }}
                      title={'Button'}
                    />
                  </View>
                </View>
              )}
            </View>
          </View>
          {/* flex direction : row and with action view */}
          {props.flexDirection === 'row' ? (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'flex-end',
                gap: 4,
              }}>
              {props.actionView ? props.actionView : null}
              <AppButton
                backgroundColor={'transparent'}
                borderColor="transparent"
                onPress={() => {
                  if (props.onPressLikeAction) {
                    props.onPressLikeAction();
                  }
                }}
                containerStyle={{
                  borderRadius: 100,
                  padding: 6,
                  height: 32,
                  width: 32,
                  backgroundColor:
                    ColorThemes.light.neutral_main_background_color,
                }}
                title={<Winicon src="outline/emoticons/heart" size={18} />}
              />
            </View>
          ) : null}
        </View>
      </View>
    </TouchableOpacity>
  );
}

export function SkeletonPlaceCard() {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View style={{ ...stylesDefault.mainContainer, marginBottom: 16 }}>
        <View style={stylesDefault.img} />
        <View style={{ flex: 1 }}>
          <View
            style={{
              width: '60%',
              height: 16,
              borderRadius: 4,
              marginBottom: 6,
              backgroundColor: '#e0e0e0',
            }}
          />
          <View
            style={{
              width: '40%',
              height: 12,
              borderRadius: 4,
              backgroundColor: '#e0e0e0',
            }}
          />
        </View>
      </View>
      <View style={stylesDefault.mainContent}>
        <View
          style={{
            width: '100%',
            height: 16,
            borderRadius: 4,
            marginBottom: 6,
            backgroundColor: '#e0e0e0',
          }}
        />
        <View
          style={{
            width: '80%',
            height: 16,
            borderRadius: 4,
            backgroundColor: '#e0e0e0',
          }}
        />
      </View>
    </SkeletonPlaceholder>
  );
}

const stylesDefault = StyleSheet.create({
  mainContainer: {
    gap: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  mainContent: {
    flex: 1,
  },
  img: {
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  inforTitle: {
    fontSize: 12,
    color: '#61616B',
  },
  titleStyle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  },
  subTitleStyle: {
    fontSize: 14,
    color: '#61616B',
  },
  bodyContentStyle: {
    fontSize: 14,
    fontWeight: '400',
    color: '#313135',
  },
});
