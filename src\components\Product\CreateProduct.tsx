import React, {
  forwardRef,
  useEffect,
  useRef,
  useState,
  useMemo,
  useCallback,
} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Image,
  PermissionsAndroid,
  Platform,
  Alert,
  KeyboardAvoidingView,
  Pressable,
  Dimensions,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import ImageCropPicker, { ImageOrVideo } from 'react-native-image-crop-picker';
import { useNavigation, useRoute } from '@react-navigation/native';
import DescriptionImage from '../Field/DescriptionImage';
import { Controller, useForm } from 'react-hook-form';
import {
  AppButton,
  AppSvg,
  ComponentStatus,
  FBottomSheet,
  FDialog,
  hideBottomSheet,
  showBottomSheet,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import { AppDispatch } from '../../redux/store/store';
import { useDispatch } from 'react-redux';
import { ColorThemes } from '../../assets/skin/colors';
import ScreenHeader from '../../Screen/Layout/header';
import ListItemLable from './LabelProduct';
import ListItem from './list/ListProductCreate';
import { set } from 'date-fns';
import { DataController } from '../../base/baseController';
import { randomGID, Ultis } from '../../utils/Utils';
import { useSelectorCustomerState } from '../../redux/hook/customerHook';
import { Snackbar } from 'react-native-paper';
import { useSelectorShopState } from '../../redux/hook/shopHook ';
import { ProductActions } from '../../redux/reducers/ProductReducer';
import ConfigAPI from '../../Config/ConfigAPI';
import { BaseDA } from '../../base/BaseDA';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import {
  faAngleRight,
  faLocation,
  faLocationArrow,
  faLocationDot,
} from '@fortawesome/free-solid-svg-icons';
import iconSvg from '../../svg/icon';
// Types
interface ProductFormData {
  image: string | null;
  ProductName: string;
  DesProduct: string;
  productType: string;
  label: string;
  listImage: string;
  price: string;
  Instock: string;
}

interface ProductState {
  image: ImageOrVideo[] | undefined;
  avataProduct: string;
  listProduct: string;
  label: string;
  lengText: {
    ProductName: number;
    DesProduct: number;
  };
}

const CreateProduct = () => {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const dispatch = useDispatch<any>();
  const popupRef = useRef<any>(null);
  const popupRef2 = useRef<any>(null);

  // Controllers
  const ProductController = useMemo(() => new DataController('Product'), []);
  const BrandController = useMemo(() => new DataController('Brand'), []);
  const CategoryController = useMemo(() => new DataController('Category'), []);

  // Selectors
  const customer = useSelectorCustomerState().data;
  const shopInfo = useSelectorShopState().data;

  // State management
  const [productState, setProductState] = useState<ProductState>({
    image: undefined,
    avataProduct: '',
    listProduct: '',
    label: '',
    lengText: {
      ProductName: 0,
      DesProduct: 0,
    },
  });

  // Form setup
  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
    watch,
  } = useForm<ProductFormData>({
    defaultValues: {
      image: null,
      ProductName: '',
      DesProduct: '',
      productType: '',
      label: '',
      listImage: '',
      price: '',
      Instock: '',
    },
  });

  // Memoized values
  const watchedValues = {
    ProductName: watch('ProductName'),
    DesProduct: watch('DesProduct'),
    image: watch('image'),
    productType: watch('productType'),
    label: watch('label'),
    price: watch('price'),
    Instock: watch('Instock'),
  };

  // Callbacks
  const getImageEdit = useCallback(async (Img: string, ImgList: string) => {
    const url = ConfigAPI.url.split('/api/')[0];
    let renderImage: any[] = [];

    if (Img) {
      const imageAvata = await BaseDA.getFilesInfor([Img]);
      setProductState(prev => ({
        ...prev,
        avataProduct: `${url}${imageAvata.data[0].Url}`,
      }));
    }

    if (ImgList) {
      const imageList = await BaseDA.getFilesInfor(ImgList.split(','));
      renderImage = imageList?.data?.map((item: any) => ({
        path: `${url}${item.Url}`,
      }));
      setProductState(prev => ({ ...prev, image: renderImage }));
    }
  }, []);

  const getTypeProductAndLabelEdit = useCallback(
    async (labelID: string, BrandId: string) => {
      const [responeCategory, responeBrandId] = await Promise.all([
        CategoryController.getListSimple({ query: `@Id: {${labelID}}` }),
        BrandController.getListSimple({ query: `@Id: {${BrandId}}` }),
      ]);

      if (responeCategory?.code === 200) {
        setProductState(prev => ({
          ...prev,
          listProduct: responeCategory.data[0]?.Name,
        }));
      }

      if (responeBrandId?.code === 200) {
        setProductState(prev => ({
          ...prev,
          label: responeBrandId.data[0]?.Name,
        }));
      }
    },
    [CategoryController, BrandController],
  );

  const pickerImg = useCallback(async () => {
    try {
      const img = await ImageCropPicker.openPicker({
        multiple: true,
        cropping: false,
        includeBase64: true,
      });

      if (img) {
        setProductState(prev => ({
          ...prev,
          image: img,
          avataProduct: img[0].path,
        }));

        const resImgs = await BaseDA.uploadFiles([
          {
            uri: img[0].path,
            type: img[0].mime,
            name: img[0].filename ?? 'file',
          },
        ]);

        if (resImgs.length > 0) {
          setValue('image', resImgs[0].Id);
          setValue('listImage', resImgs.map((img: any) => img.Id).toString());
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi chọn ảnh',
        status: ComponentStatus.ERROR,
      });
    }
  }, [setValue]);

  const formatCurrency = useCallback((value: string) => {
    const numericValue = value.replace(/[^0-9]/g, '');
    if (!numericValue) return '';
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }, []);

  const onSubmit = useCallback(
    async (data: ProductFormData) => {
      try {
        const productData = {
          Id: route?.params?.dataEdit
            ? route?.params?.dataEdit?.Id
            : randomGID(),
          DateCreated: route?.params?.dataEdit
            ? route?.params?.dataEdit?.DateCreated
            : Date.now(),
          Name: data.ProductName,
          Description: data.DesProduct,
          Img: data.image,
          Price: data.price,
          Status: 1,
          Content: productState.listProduct,
          ListImg: data.listImage.toString(),
          Instock: data.Instock,
          CategoryId: data.productType,
          BrandId: data.label,
          ShopId: shopInfo[0]?.Id,
        };

        if (route?.params?.title === 'Chỉnh sửa sản phẩm') {
          const response = await ProductController.edit([productData]);
          if (response.code === 200) {
            showSnackbar({
              message: 'Cập nhật sản phẩm thành công',
              status: ComponentStatus.SUCCSESS,
            });
          }
        } else {
          const response = await ProductController.add([productData]);
          if (response.code === 200) {
            showSnackbar({
              message: 'Tạo sản phẩm mới thành công',
              status: ComponentStatus.SUCCSESS,
            });
            dispatch(ProductActions.getInforProduct(shopInfo[0].Id));
            navigation.goBack();
          }
        }
      } catch (error) {
        console.error('Error submitting product:', error);
        showSnackbar({
          message: 'Có lỗi xảy ra khi lưu sản phẩm',
          status: ComponentStatus.ERROR,
        });
      }
    },
    [
      route?.params,
      productState.listProduct,
      shopInfo,
      ProductController,
      dispatch,
      navigation,
    ],
  );

  // Effects
  useEffect(() => {
    if (route?.params?.dataEdit) {
      const { dataEdit } = route.params;
      setValue('image', dataEdit.Img);
      setValue('ProductName', dataEdit.Name);
      setValue('DesProduct', dataEdit.Description);
      setValue('productType', dataEdit.CategoryId);
      setValue('label', dataEdit.BrandId);
      setValue('listImage', dataEdit.ListImg.split(','));
      setValue('price', dataEdit.Price);
      setValue('Instock', dataEdit.Instock);

      getImageEdit(dataEdit.Img, dataEdit.ListImg);
      getTypeProductAndLabelEdit(dataEdit.CategoryId, dataEdit.BrandId);
    }
  }, [
    route?.params?.dataEdit,
    setValue,
    getImageEdit,
    getTypeProductAndLabelEdit,
  ]);

  useEffect(() => {
    if (route?.params?.title === 'Copy sản phẩm' && route?.params?.dataCopy) {
      const { dataCopy } = route.params;
      setValue('image', dataCopy.Img);
      setValue('ProductName', dataCopy.Name);
      setValue('DesProduct', dataCopy.Description);
      setValue('productType', dataCopy.CategoryId);
      setValue('label', dataCopy.BrandId);
      setValue('listImage', dataCopy.ListImg.split(','));
      setValue('price', dataCopy.Price);
      setValue('Instock', dataCopy.Instock);

      getImageEdit(dataCopy.Img, dataCopy.ListImg);
      getTypeProductAndLabelEdit(dataCopy.CategoryId, dataCopy.BrandId);
    }
  }, [
    route?.params?.title,
    route?.params?.dataCopy,
    setValue,
    getImageEdit,
    getTypeProductAndLabelEdit,
  ]);

  useEffect(() => {
    setProductState(prev => ({
      ...prev,
      lengText: {
        ProductName: watchedValues.ProductName?.length || 0,
        DesProduct: watchedValues.DesProduct?.length || 0,
      },
    }));
  }, [watchedValues.ProductName, watchedValues.DesProduct]);

  return (
    <KeyboardAvoidingView
      style={{ flex: 1, position: 'relative' }}
      behavior="padding"
      enabled>
      <ScrollView>
        <View style={styles.container}>
          <FBottomSheet ref={popupRef} />
          <FBottomSheet ref={popupRef2} />
          <Controller
            control={control}
            name="image"
            rules={{
              required: 'Vui lòng chọn ảnh',
            }}
            render={({ field: { value, onChange } }) => (
              <DescriptionImage
                image={productState.image as ImageOrVideo[]}
                pickerImg={pickerImg}
                avataProduct={productState.avataProduct}
                imageListEdit={productState.image as ImageOrVideo[]}
              />
            )}
          />
          {errors.image && watch('image') == null && (
            <Text style={{ color: 'red', marginLeft: 10, fontFamily: 'roboto' }}>
              {errors.image.message}
            </Text>
          )}

          <View style={styles.section}>
            <View
              style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={styles.label}>Tên sản phẩm *</Text>
              <Text style={styles.limit}>
                {productState.lengText.ProductName}/50
              </Text>
            </View>
            <Controller
              control={control}
              name="ProductName"
              rules={{
                required: 'Vui lòng nhập tên sản phẩm',
              }}
              render={({ field: { value, onChange } }) => (
                <TextInput
                  style={{ height: 40 }}
                  placeholder="Nhập tên sản phẩm"
                  placeholderTextColor="#DDDDDD"
                  maxLength={50}
                  value={value}
                  onChange={e => onChange(e.nativeEvent.text)}
                />
              )}
            />
          </View>
          {errors.ProductName && watch('ProductName') == '' && (
            <Text style={{ color: 'red', marginLeft: 10, fontFamily: 'roboto' }}>
              {errors.ProductName.message}
            </Text>
          )}

          <View style={styles.section}>
            <View
              style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={styles.label}>Mô tả sản phẩm *</Text>
              <Text style={styles.limit}>
                {productState.lengText.DesProduct}/3000
              </Text>
            </View>
            <Controller
              control={control}
              name="DesProduct"
              rules={{
                required: 'Vui lòng nhập mô tả sản phẩm',
              }}
              render={({ field: { value, onChange } }) => (
                <TextInput
                  style={{ height: 40, backgroundColor: 'white' }}
                  placeholder="Mô tả sản phẩm"
                  placeholderTextColor="#DDDDDD"
                  value={value}
                  onChange={e => onChange(e.nativeEvent.text)}
                />
              )}
            />
          </View>
          {errors.DesProduct && watch('DesProduct') == '' && (
            <Text style={{ color: 'red', marginLeft: 10, fontFamily: 'roboto' }}>
              {errors.DesProduct.message}
            </Text>
          )}

          <View>
            <View>
              <Controller
                control={control}
                name="productType"
                rules={{
                  required: 'Vui lòng chọn danh mục sản phẩm',
                }}
                render={({ field: { value } }) => (
                  <TouchableOpacity
                    style={styles.option}
                    onPress={() => {
                      showBottomSheet({
                        ref: popupRef2,
                        enableDismiss: true,
                        children: (
                          <BottomSheetComments
                            type={'Product'}
                            ref={popupRef2}
                            handleSelect={(value: any) => {
                              setProductState(prev => ({
                                ...prev,
                                listProduct: value?.name,
                              }));
                              setValue('productType', value?.id);
                            }}
                          />
                        ),
                      });
                    }}>
                    <View
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <AppSvg SvgSrc={iconSvg.listProduct} size={16} />
                      <Text
                        style={{
                          marginLeft: 10,
                          color:
                            productState.listProduct == ''
                              ? '#DDDDDD'
                              : 'black',
                        }}>
                        {' '}
                        {productState.listProduct
                          ? productState.listProduct
                          : 'Danh mục sản phẩm *'}
                      </Text>
                    </View>
                    <Text style={styles.optionPlaceholder}>
                      <FontAwesomeIcon
                        icon={faAngleRight}
                        color={ColorThemes.light.black}
                        size={16}
                      />
                    </Text>
                  </TouchableOpacity>
                )}
              />
              {errors.productType && watch('productType') == '' && (
                <Text
                  style={{ color: 'red', marginLeft: 10, fontFamily: 'roboto' }}>
                  {errors.productType.message}
                </Text>
              )}
            </View>
            <View>
              <Controller
                control={control}
                name="label"
                rules={{
                  required: 'Vui lòng chọn Nhãn sản phẩm',
                }}
                render={({ field: { value } }) => (
                  <TouchableOpacity
                    style={styles.option}
                    onPress={() => {
                      showBottomSheet({
                        ref: popupRef,
                        enableDismiss: true,
                        children: (
                          <BottomSheetComments
                            type={'label'}
                            ref={popupRef}
                            handleSelectLabel={(value: any) => {
                              setProductState(prev => ({
                                ...prev,
                                label: value?.name,
                              }));
                              setValue('label', value?.id);
                            }}
                          />
                        ),
                      });
                    }}>
                    <View
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <AppSvg SvgSrc={iconSvg.label} size={16} />
                      <Text
                        style={{
                          marginLeft: 10,
                          color: productState.label ? 'black' : '#DDDDDD',
                        }}>
                        {' '}
                        {productState.label
                          ? productState.label
                          : 'Nhãn hàng *'}
                      </Text>
                    </View>
                    <Text style={styles.optionPlaceholder}>
                      <FontAwesomeIcon
                        icon={faAngleRight}
                        color={ColorThemes.light.black}
                        size={16}
                      />
                    </Text>
                  </TouchableOpacity>
                )}
              />
              {errors.label && watch('label') == '' && (
                <Text
                  style={{ color: 'red', marginLeft: 10, fontFamily: 'roboto' }}>
                  {errors.label.message}
                </Text>
              )}
            </View>
            <View style={styles.option}>
              <Controller
                control={control}
                name="price"
                rules={{
                  required: 'Vui lòng chọn giá sản phẩm',
                }}
                render={({ field: { value, onChange } }) => (
                  <View
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <AppSvg SvgSrc={iconSvg.price} size={16} />
                    <TextInput
                      style={{ height: 40, marginLeft: 8, width: 300 }}
                      placeholder="Nhập giá sản phẩm"
                      keyboardType="numeric"
                      placeholderTextColor="#DDDDDD"
                      value={formatCurrency(value)}
                      onChange={e => onChange(e.nativeEvent.text)}
                    />
                  </View>
                )}
              />
              <Text style={styles.optionPlaceholder}>
                <Text style={{ color: 'blue', alignItems: 'center' }}>VNĐ</Text>
              </Text>
            </View>
            {errors.price && watch('price') == '' && (
              <Text
                style={{ color: 'red', marginLeft: 10, fontFamily: 'roboto' }}>
                {errors.price.message}
              </Text>
            )}
            <TouchableOpacity style={styles.option}>
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                }}>
                <Controller
                  control={control}
                  name="Instock"
                  rules={{
                    required: 'Vui lòng nhập số tồn kho',
                  }}
                  render={({ field: { value, onChange } }) => (
                    <View>
                      <View
                        style={{
                          display: 'flex',
                          flexDirection: 'row',
                          alignItems: 'center',
                        }}>
                        <AppSvg SvgSrc={iconSvg.price} size={16} />
                        <TextInput
                          style={{ height: 40, marginLeft: 8, width: 350 }}
                          placeholder="Tồn kho"
                          placeholderTextColor="#DDDDDD"
                          keyboardType="numeric"
                          value={value}
                          onChange={e => onChange(e.nativeEvent.text)}
                        />
                      </View>
                    </View>
                  )}
                />
              </View>
            </TouchableOpacity>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: 100,
              }}>
              <View>
                {errors.Instock && watch('Instock') == '' && (
                  <Text
                    style={{
                      color: 'red',
                      marginLeft: 10,
                      fontFamily: 'roboto',
                    }}>
                    {errors.Instock.message}
                  </Text>
                )}
              </View>
            </View>
          </View>
        </View>
        <TouchableOpacity
          style={styles.buyButton}
          onPress={handleSubmit(onSubmit)}>
          <Text style={styles.actionButtonText}>
            {route?.params?.title == 'Chỉnh sửa sản phẩm'
              ? 'Chỉnh sửa'
              : 'Tạo mới'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const BottomSheetComments = forwardRef(function BottomSheetComments(
  data: {
    handleSelect?: any;
    handleSelectLabel?: any;
    handleSelectFrom?: any;
    type: any;
  },
  ref: any,
) {
  const dispatch: AppDispatch = useDispatch();
  const { type, handleSelect, handleSelectLabel, handleSelectFrom } = data;
  const BrandController = new DataController('Brand');
  const CategoryController = new DataController('Category');
  const [selecChildID, setSelecChildID] = useState<string>('');
  const [selecChildName, setSelecChildName] = useState<string>('');
  const [dataLabel, setDataLabel] = useState<any[]>();
  const [dataProduct, setDataProduct] = useState<any>();
  const [searchData, setSearchData] = useState<string>('');
  const [selectItemChild, setSelectItemChild] = useState<any>();
  const [isSelected, setIsSelected] = useState('');

  const shopInfo = useSelectorShopState().data;

  const handleSubmit = (selecChildID: string, selecChildName: string) => {
    if (!selecChildID || !selecChildName) {
      return;
    }
    if (selecChildID) {
      if (type === 'label') {
        handleSelectLabel({
          id: selecChildID,
          name: selecChildName,
        });
        hideBottomSheet(ref);
      } else if (type === 'Product') {
        handleSelect({
          id: selecChildID,
          name: selecChildName,
        });
        hideBottomSheet(ref);
      }
    }
  };
  let callApiBrand = async () => {
    let respone = await BrandController.aggregateList({
      page: 1,
      size: 10,
    });
    if (respone && respone?.code == 200) {
      setDataLabel(respone?.data);
    }
  };

  let callApiCategory = async () => {
    let respone = await CategoryController.aggregateList({
      page: 1,
      size: 10,
    });
    if (respone && respone?.code == 200) {
      setDataProduct(respone?.data);
    }
  };
  useEffect(() => {
    if (type === 'label') {
      callApiBrand();
    }
    if (type === 'Product') {
      callApiCategory();
    }
  }, [type]);

  return (
    <Pressable
      style={{
        width: '100%',
        height: Dimensions.get('window').height - 100,
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        position: 'relative',
      }}>
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.transparent,
          paddingVertical: 4,
        }}
        title={type && type == 'label' ? `Chọn thương hiệu` : `Chọn danh mục`}
        prefix={
          <View style={{ flexDirection: 'row' }}>
            <TouchableOpacity
              onPress={() => (hideBottomSheet(ref), setSelectItemChild(null))}
              style={{ padding: 12, alignItems: 'center' }}>
              <Winicon
                src="outline/layout/xmark"
                size={24}
                color={ColorThemes.light.neutral_text_body_color}
              />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => setSelectItemChild(null)}
              style={{ padding: 12, alignItems: 'center' }}>
              <Winicon
                src="color/arrows/arrow-left"
                size={24}
                color={ColorThemes.light.neutral_text_body_color}
              />
            </TouchableOpacity>
          </View>
        }
      />
      <KeyboardAvoidingView
        style={{ flex: 1, position: 'relative' }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'} // Adjust for iOS and Android
        keyboardVerticalOffset={Platform.OS === 'ios' ? 65 : 0} // Offset for iOS
      >
        <View
          style={{
            padding: 10,
            backgroundColor: '#fff',
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 10,
            }}>
            <TextInput
              style={{
                flex: 1,
                backgroundColor: '#f0f0f0',
                borderRadius: 5,
                padding: 8,
                marginRight: 10,
              }}
              placeholder="Search"
              value={searchData}
              onChange={e => setSearchData(e.nativeEvent.text)}
            />

            <TouchableOpacity>
              <Text
                style={{
                  color: '#007AFF',
                  fontSize: 16,
                }}>
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        <View style={{ height: 1000, marginBottom: 50 }}>
          {type == 'label' ? (
            <ListItemLable
              setSelecChildID={setSelecChildID}
              setSelecChildName={setSelecChildName}
              dataLabel={dataLabel as any[]}
            />
          ) : null}
          {type == 'Product' ? (
            <ListItem
              setSelecChildID={setSelecChildID}
              setSelecChildName={setSelecChildName}
              dataProduct={dataProduct as any[]}
              selectItemChild={selectItemChild}
              setSelectItemChild={setSelectItemChild}
            />
          ) : null}
          <Pressable style={{ flex: 1 }}></Pressable>
        </View>
        <View
          style={{
            flex: 1,
            marginBottom: 10,
            position: 'absolute',
            bottom: 2,
            width: '100%',
          }}>
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              alignItems: 'center',
              justifyContent: 'space-around',
              position: 'absolute',
              bottom: 0,
            }}>
            <TouchableOpacity style={styles.cancelButton}>
              <Text
                style={styles.buttonText}
                onPress={() => hideBottomSheet(ref)}>
                Đóng
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.confirmButton}
              onPress={() => handleSubmit(selecChildID, selecChildName)}>
              <Text style={styles.buttonText}>Xác nhận</Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Pressable>
  );
});

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F0F0F0',
    position: 'relative',
  },
  section: {
    marginTop: 10,
    marginLeft: 10,
    marginRight: 10,
    backgroundColor: 'white',
    borderRadius: 5,
    padding: 10,
    elevation: 2, // Tạo bóng cho Android
    shadowColor: '#A9A9A9', // Tạo bóng cho iOS
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  imageText: {
    color: '#E14337',
    marginLeft: 21,
    fontWeight: 100,
    fontSize: 12,
  },
  label: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#333',
  },
  imagePlaceholder: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderStyle: 'dashed',
    width: 73,
    height: 73,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 5,
    marginTop: 10,
    marginLeft: 10,
  },
  placeholderText: {
    color: '#888',
    fontSize: 14,
  },
  input: {
    borderRadius: 5,
    padding: 10,
    fontSize: 14,
    color: '#555555',
  },
  inputMoney: {
    flex: 1,
    fontSize: 16,
    color: 'black',
    marginLeft: 10,
  },
  multilineInput: {
    height: 50,
    textAlignVertical: 'top',
  },
  limit: {
    fontSize: 12,
    color: '#888',
    textAlign: 'right',
    marginTop: 5,
  },
  option: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    borderRadius: 5,
    marginTop: 10,
    marginRight: 10,
    marginLeft: 10,
    backgroundColor: 'white',
    height: 55,
  },

  optionPlaceholder: {
    fontSize: 14,
    color: '#888',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },

  optionPlaceholderLastItem: {
    fontSize: 14,
    color: '#888',
    height: 40,
    borderLeftColor: '#00FFFF',
    borderLeftWidth: 0.4,
    minWidth: 60,
  },
  buyButton: {
    backgroundColor: 'blue',
    width: '80%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50,
    borderRadius: 30,
    margin: 'auto',
    position: 'absolute',
    bottom: 40,
    left: '11%',
  },

  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  cancelButton: {
    backgroundColor: '#f0f0f0', // Màu xám nhạt cho nút "Đóng"
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ccc',
    width: '45%',
  },
  confirmButton: {
    backgroundColor: '#007AFF', // Màu xanh cho nút "Xác nhận"
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    width: '45%',
  },
  buttonText: {
    color: '#000', // Màu chữ đen cho nút "Đóng"
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default CreateProduct;
