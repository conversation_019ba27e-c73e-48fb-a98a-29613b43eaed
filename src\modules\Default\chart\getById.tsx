import React, {useEffect, useMemo, useRef, useState} from 'react';
import {Text, View} from 'react-native';
import RenderChartByType, {EChartType} from './getByType';
import {DropdownSelect, FPopup} from 'wini-mobile-components';
import {
  <PERSON><PERSON><PERSON>roller,
  SettingDataController,
} from '../../../base/baseController';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';

export default function ChartById({
  id,
  searchRaw = '',
  style,
  chartStyle,
  filterAll = false,
  onPress = true,
}: any) {
  const now = new Date();

  const [chartItem, setChartItem] = useState<any>(undefined);
  const [result, setResult] = useState<Array<any>>([]);
  const popupRef = useRef<any>(null);
  const listTime = useMemo(() => {
    if (!chartItem) return [];
    switch (chartItem?.Type) {
      case EChartType.line:
      case EChartType.bar:
      case EChartType.horizontalBar:
        return [
          {id: 'thisWeek', name: '<PERSON><PERSON>n này'},
          {id: 'lastWeek', name: 'Tuần trước'},
          {id: 'thisMonth', name: 'Tháng này'},
          {id: 'lastMonth', name: 'Tháng trước'},
          {id: 'lastThreeMonth', name: '3 tháng này'},
          {id: 'lastSixMonth', name: '6 tháng này'},
          // { id: "thisYear", name: "Cả năm" },
        ];
      default:
        return [7, 30, 45, 60, 90, ...(filterAll ? [Infinity] : [])].map(
          (e, i) => ({id: e, name: i === 5 ? 'Tất cả' : `${e} ngày qua`}),
        );
    }
  }, [chartItem?.Type]);
  const [selectedTime, setSelectedTime] = useState<any>();
  const groupByRegex = /(GROUPBY\s+\d+\s+(?:@\w+\s*)+)/g;

  const getData = async () => {
    let querySearch = chartItem.Query.trim() === '*' ? '' : chartItem.Query;
    if (searchRaw.length) querySearch += ` ${searchRaw}`;
    var startDate = undefined;
    var endDate = undefined;
    var reducers = undefined;
    switch (selectedTime) {
      case 'thisWeek':
        startDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate() - (now.getDay() ? now.getDay() : 7) + 1,
        ).getTime();
        endDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate() - (now.getDay() ? now.getDay() : 7) + 7,
          23,
          59,
          59,
          999,
        ).getTime();
        reducers = chartItem.Group.replace(
          groupByRegex,
          (m: string, _: any) => {
            const splitV = m.split(' ');
            return `APPLY "dayofweek(@DateCreated / 1000)" AS _dayofweek ${splitV[0].trim()} ${
              parseInt(splitV[1]) + 1
            } ${['@_dayofweek', ...splitV.slice(2)].join(' ')}`;
          },
        );
        break;
      case 'lastWeek':
        startDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate() - (now.getDay() ? now.getDay() : 7) - 6,
        ).getTime();
        endDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate() - (now.getDay() ? now.getDay() : 7),
          23,
          59,
          59,
          999,
        ).getTime();
        reducers = chartItem.Group.replace(
          groupByRegex,
          (m: string, _: any) => {
            const splitV = m.split(' ');
            return `APPLY "dayofweek(@DateCreated / 1000)" AS _dayofweek ${splitV[0].trim()} ${
              parseInt(splitV[1]) + 1
            } ${['@_dayofweek', ...splitV.slice(2)].join(' ')}`;
          },
        );
        break;
      case 'thisMonth':
        startDate = new Date(now.getFullYear(), now.getMonth()).getTime();
        endDate = new Date(
          now.getFullYear(),
          now.getMonth() + 1,
          0,
          23,
          59,
          59,
          999,
        ).getTime();
        reducers = chartItem.Group.replace(
          groupByRegex,
          (m: string, _: any) => {
            const splitV = m.split(' ');
            return `APPLY "floor(dayofmonth(@DateCreated / 1000) / 7)" AS _dayofmonth ${splitV[0].trim()} ${
              parseInt(splitV[1]) + 1
            } ${['@_dayofmonth', ...splitV.slice(2)].join(' ')}`;
          },
        );
        break;
      case 'lastMonth':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1).getTime();
        endDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          0,
          23,
          59,
          59,
          999,
        ).getTime();
        reducers = chartItem.Group.replace(groupByRegex, (m: any, _: any) => {
          const splitV = m.split(' ');
          return `APPLY "floor(dayofmonth(@DateCreated / 1000) / 7)" AS _dayofmonth ${splitV[0].trim()} ${
            parseInt(splitV[1]) + 1
          } ${['@_dayofmonth', ...splitV.slice(2)].join(' ')}`;
        });
        break;
      case 'lastThreeMonth':
        startDate = new Date(now.getFullYear(), now.getMonth() - 3).getTime();
        endDate = new Date(
          now.getFullYear(),
          now.getMonth() + 1,
          0,
          23,
          59,
          59,
          999,
        ).getTime();
        reducers = chartItem.Group.replace(groupByRegex, (m: any, _: any) => {
          const splitV = m.split(' ');
          return `APPLY "monthofyear(@DateCreated / 1000)" AS _monthofyear ${splitV[0].trim()} ${
            parseInt(splitV[1]) + 1
          } ${['@_monthofyear', ...splitV.slice(2)].join(' ')}`;
        });
        break;
      case 'lastSixMonth':
        startDate = new Date(now.getFullYear(), now.getMonth() - 6).getTime();
        endDate = new Date(
          now.getFullYear(),
          now.getMonth() + 1,
          0,
          23,
          59,
          59,
          999,
        ).getTime();
        reducers = chartItem.Group.replace(groupByRegex, (m: any, _: any) => {
          const splitV = m.split(' ');
          return `APPLY "monthofyear(@DateCreated / 1000)" AS _monthofyear ${splitV[0].trim()} ${
            parseInt(splitV[1]) + 1
          } ${['@_monthofyear', ...splitV.slice(2)].join(' ')}`;
        });
        break;
      default:
        if (selectedTime === Infinity) startDate = undefined;
        else
          startDate = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDay() - selectedTime,
          ).getTime();
        reducers = chartItem.Group;
        break;
    }
    querySearch += ` ${
      startDate ? `@DateCreated:[${startDate} ${endDate ?? Date.now()}]` : ''
    }`;
    querySearch = querySearch.trim();

    const controller = new DataController(chartItem.TbName);

    const res = await controller.group({
      searchRaw: querySearch.length ? querySearch : '*',
      reducers: reducers,
    });

    if (res.code === 200) setResult(res.data);
  };

  const getMonthName = (m: any) => {
    switch (m) {
      case 0:
        return 'T1';
      case 1:
        return 'T2';
      case 2:
        return 'T3';
      case 3:
        return 'T4';
      case 4:
        return 'T5';
      case 5:
        return 'T6';
      case 6:
        return 'T7';
      case 7:
        return 'T8';
      case 8:
        return 'T9';
      case 9:
        return 'T10';
      case 10:
        return 'T11';
      case 11:
        return 'T12';
      default:
        return '';
    }
  };

  const getxAxisName = () => {
    switch (selectedTime) {
      case 'thisWeek':
      case 'lastWeek':
        return ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'];
      case 'thisMonth':
      case 'lastMonth':
        return ['1 - 7', '8 - 14', '15 - 21', '22 - cuối'];
      case 'lastThreeMonth':
        return [
          new Date(now.getFullYear(), now.getMonth() - 2).getMonth(),
          new Date(now.getFullYear(), now.getMonth() - 1).getMonth(),
          now.getMonth(),
        ].map(num => getMonthName(num));
      case 'lastSixMonth':
        return [
          new Date(now.getFullYear(), now.getMonth() - 5).getMonth(),
          new Date(now.getFullYear(), now.getMonth() - 4).getMonth(),
          new Date(now.getFullYear(), now.getMonth() - 3).getMonth(),
          new Date(now.getFullYear(), now.getMonth() - 2).getMonth(),
          new Date(now.getFullYear(), now.getMonth() - 1).getMonth(),
          now.getMonth(),
        ].map(num => getMonthName(num));
      case 'thisYear':
        return Array.from({length: 12}).map((_, index) => index + 1);
      default:
        return [];
    }
  };

  useEffect(() => {
    if (listTime.length) setSelectedTime(listTime[0].id);
  }, [listTime]);

  useEffect(() => {
    if (selectedTime) getData();
  }, [selectedTime]);

  useEffect(() => {
    if (id) {
      const controller = new SettingDataController('chart');
      controller.getByIds([id]).then(async res => {
        if (res.code === 200) {
          const tmp = res.data[0];

          tmp.Setting = JSON.parse(tmp.Setting);

          setChartItem(tmp);
        }
      });
    }
  }, [id]);

  return (
    <View
      style={{
        ...style,
        borderColor: ColorThemes.light.neutral_main_border_color,
        borderRadius: 8,
        borderWidth: 1,
        padding: 16,
      }}>
      <FPopup ref={popupRef} />
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          width: '100%',
          justifyContent: 'space-between',
        }}>
        {chartItem ? (
          <Text numberOfLines={2} style={{...TypoSkin.title5}}>
            {chartItem?.Name ?? ''}
          </Text>
        ) : null}
        {listTime?.length ? (
          <DropdownSelect
            style={{width: '45%'}}
            value={selectedTime}
            data={listTime}
            onChange={(v: any) => {
              setSelectedTime(v.id);
            }}
          />
        ) : null}
      </View>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          width: '100%',
          justifyContent: 'space-between',
        }}>
        {chartItem ? (
          <RenderChartByType
            onPressSelected={
              onPress
                ? ev => () => {
                    console.log('====================================');
                    console.log(ev);
                    console.log('====================================');
                  }
                : undefined
            }
            style={chartStyle ?? {height: 150, gap: 24}}
            type={chartItem.Type}
            xAxisName={
              typeof listTime[0] === 'number' ? undefined : getxAxisName()
            }
            datasets={chartItem.Setting.datasets.map((e: any) => {
              try {
                const data = result;
                var listData = Array<any>();
                var filterByTime = undefined;
                switch (selectedTime) {
                  case 'thisWeek':
                  case 'lastWeek':
                    listData = [1, 2, 3, 4, 5, 6, 0];
                    filterByTime = (ev: any, num: number) =>
                      ev?.['_dayofweek'] &&
                      parseInt(ev?.['_dayofweek']) === num;
                    break;
                  case 'thisMonth':
                  case 'lastMonth':
                    listData = [0, 1, 2, 3];
                    filterByTime = (ev: any, num: number) =>
                      num === 3
                        ? parseInt(ev._dayofmonth) >= num
                        : parseInt(ev._dayofmonth) === num;
                    break;
                  case 'lastThreeMonth':
                    listData = [
                      new Date(
                        now.getFullYear(),
                        now.getMonth() - 2,
                      ).getMonth(),
                      new Date(
                        now.getFullYear(),
                        now.getMonth() - 1,
                      ).getMonth(),
                      now.getMonth(),
                    ];
                    filterByTime = (ev: any, num: number) =>
                      parseInt(ev._monthofyear) === num;
                    break;
                  case 'lastSixMonth':
                    listData = [
                      new Date(
                        now.getFullYear(),
                        now.getMonth() - 5,
                      ).getMonth(),
                      new Date(
                        now.getFullYear(),
                        now.getMonth() - 4,
                      ).getMonth(),
                      new Date(
                        now.getFullYear(),
                        now.getMonth() - 3,
                      ).getMonth(),
                      new Date(
                        now.getFullYear(),
                        now.getMonth() - 2,
                      ).getMonth(),
                      new Date(
                        now.getFullYear(),
                        now.getMonth() - 1,
                      ).getMonth(),
                      now.getMonth(),
                    ];
                    filterByTime = (ev: any, num: number) =>
                      parseInt(ev._monthofyear) === num;
                    break;
                  case 'thisYear':
                    break;
                  default:
                    break;
                }
                var evalValue = new Function(
                  'data',
                  'listData',
                  'filterByTime',
                  `return ${e?.value}`,
                )(data, listData, filterByTime);
              } catch (error) {
                console.log('=======error=======');
                console.log(error);
                console.log('===================');
                evalValue = 0;
              }
              return {
                ...e,
                value: evalValue,
              };
            })}
            unit={chartItem.Setting.unit}
            legend={chartItem.Setting.legend}
          />
        ) : null}
      </View>
    </View>
  );
}
