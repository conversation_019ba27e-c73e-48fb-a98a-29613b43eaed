import React, {useRef, useState, useEffect} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Text,
  Dimensions,
  StatusBar,
  Platform,
} from 'react-native';
import Video from 'react-native-video';
import Orientation from 'react-native-orientation-locker';
import {Winicon} from 'wini-mobile-components';
import Slider from '@react-native-community/slider';
const {width} = Dimensions.get('window');

export const VideoPlayer = ({
  source,
  onProgressPercent,
  onLoad,
  onError,
  disableFullscreen = false,
  onFullscreenChange,
  customFullscreenUI = false,
}: {
  source: string;
  onProgressPercent?: (percent: number) => void;
  onLoad?: (time: number) => void;
  onError?: (error: any) => void;
  disableFullscreen?: boolean;
  onFullscreenChange?: (isFullscreen: boolean) => void;
  customFullscreenUI?: boolean;
}) => {
  const videoRef = useRef<any>(null);
  const [paused, setPaused] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const [fullscreen, setFullscreen] = useState(false);
  const [isSliding, setIsSliding] = useState(false); // tránh xung đột khi đang kéo
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Theo dõi và xử lý thay đổi orientation
  useEffect(() => {
    console.log('Setting up orientation listeners');

    // Đăng ký listener để theo dõi thay đổi orientation
    const orientationListener = (orientation: string) => {
      console.log('Orientation changed to:', orientation);
      // Cập nhật trạng thái fullscreen dựa trên orientation thực tế
      if (orientation === 'LANDSCAPE-LEFT' || orientation === 'LANDSCAPE-RIGHT') {
        if (!fullscreen) {
          setFullscreen(true);
        }
      } else if (orientation === 'PORTRAIT') {
        if (fullscreen) {
          setFullscreen(false);
        }
      }
    };

    Orientation.addOrientationListener(orientationListener);

    // Reset to portrait when component unmounts
    return () => {
      console.log('Component unmounting, resetting orientation');
      Orientation.removeOrientationListener(orientationListener);
      if (fullscreen) {
        try {
          Orientation.lockToPortrait();
        } catch (error) {
          console.error('Error resetting orientation:', error);
        }
      }
    };
  }, [fullscreen]);

  // Reset states when source changes
  useEffect(() => {
    setIsLoading(true);
    setHasError(false);
    setIsCompleted(false);
    setPaused(false);
    setCurrentTime(0);
    setDuration(0);
  }, [source]);

  const handleLoad = (data: any) => {
    setDuration(data.duration);
    setIsLoading(false);
    if (onLoad) {
      onLoad(data.duration);
    }
  };

  const handleError = (error: any) => {
    console.error('Video error:', error);
    setHasError(true);
    setIsLoading(false);
    if (onError) {
      onError(error);
    }
  };

  const handleProgress = (data: any) => {
    // Không cập nhật currentTime khi đang kéo slider
    if (!isSliding) {
      const percent = duration > 0 ? (data.currentTime / duration) * 100 : 0;
      console.log('percent', percent);

      setCurrentTime(data.currentTime);
      // Gọi hàm từ cha truyền vào
      if (onProgressPercent) {
        onProgressPercent(percent);
      }
    }
  };

  const handleEnd = () => {
    setIsCompleted(true);
    setPaused(true);
  };

  const handleSeek = (seconds: number) => {
    const newTime = Math.max(0, Math.min(currentTime + seconds, duration));
    videoRef.current?.seek(newTime);
    setCurrentTime(newTime);
  };
  const onSlidingStart = () => setIsSliding(true);
  const onSlidingComplete = (value: any) => {
    videoRef.current?.seek(value);
    setCurrentTime(value);
    setIsSliding(false);
  };
  const toggleFullscreen = () => {
    // Nếu sử dụng custom fullscreen UI, chỉ gọi callback
    if (customFullscreenUI) {
      if (onFullscreenChange) {
        onFullscreenChange(!fullscreen);
      }
      return;
    }

    // Xử lý fullscreen mặc định
    if (!fullscreen) {
      try {
        // Đặt fullscreen trước để UI cập nhật ngay lập tức
        setFullscreen(true);

        // Gọi callback nếu có
        if (onFullscreenChange) {
          onFullscreenChange(true);
        }

        // Thử các phương thức khác nhau để đảm bảo hoạt động trên nhiều thiết bị
        if (Platform.OS === 'ios') {
          // iOS có thể cần cách tiếp cận khác
          setTimeout(() => {
            Orientation.lockToLandscape();
          }, 100);
        } else {
          // Android
          Orientation.lockToLandscape();

          // Một số thiết bị Android cần phương thức cụ thể hơn
          if (Orientation.lockToLandscapeLeft) {
            setTimeout(() => {
              Orientation.lockToLandscapeLeft();
            }, 50);
          }
        }

        console.log('Switching to landscape mode');
      } catch (error) {
        console.error('Error switching to landscape:', error);
        // Đã đặt fullscreen ở trên rồi, không cần làm gì thêm
      }
    } else {
      try {
        // Đặt fullscreen trước để UI cập nhật ngay lập tức
        setFullscreen(false);

        // Gọi callback nếu có
        if (onFullscreenChange) {
          onFullscreenChange(false);
        }

        // Thử các phương thức khác nhau để đảm bảo hoạt động trên nhiều thiết bị
        if (Platform.OS === 'ios') {
          // iOS có thể cần cách tiếp cận khác
          setTimeout(() => {
            Orientation.lockToPortrait();
          }, 100);
        } else {
          // Android
          Orientation.lockToPortrait();
        }

        console.log('Switching to portrait mode');
      } catch (error) {
        console.error('Error switching to portrait:', error);
        // Đã đặt fullscreen ở trên rồi, không cần làm gì thêm
      }
    }
  };

  // Hàm getProgress đã được thay thế bằng Slider component

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  const renderControls = () => (
    <View style={[styles.controls, fullscreen && styles.fullscreenControls]}>
      <TouchableOpacity
        onPress={() => handleSeek(-10)}
        style={styles.controlBtn}>
        <Winicon
          src="outline/arrows/ctrl-backward"
          size={fullscreen ? 24 : 16}
          color="#fff"
        />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => setPaused(!paused)}
        style={styles.controlBtn}>
        <Winicon
          src={
            paused
              ? 'color/multimedia/btn-play-2'
              : 'color/multimedia/button-pause'
          }
          size={fullscreen ? 24 : 16}
          color="#fff"
        />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => handleSeek(10)}
        style={styles.controlBtn}>
        <Winicon
          src="outline/arrows/ctrl-forward"
          size={fullscreen ? 24 : 16}
          color="#fff"
        />
      </TouchableOpacity>

      {/* Chỉ hiển thị nút fullscreen khi không bị vô hiệu hóa */}
      {!disableFullscreen && (
        <TouchableOpacity onPress={toggleFullscreen} style={styles.controlBtn}>
          <Winicon
            src={fullscreen ? 'fill/arrows/fullscreen-exit' : 'fill/arrows/fullscreen'}
            size={fullscreen ? 24 : 16}
            color="#fff"
          />
        </TouchableOpacity>
      )}

      {/* Hiển thị thời gian khi ở chế độ fullscreen */}
      {fullscreen && (
        <Text style={styles.fullscreenTimeText}>
          {formatTime(currentTime)} / {formatTime(duration)}
        </Text>
      )}
    </View>
  );

  const renderVideo = () => (
    <View style={fullscreen ? styles.fullscreenVideo : styles.videoWrapper}>
     

      {hasError ? (
        <View style={styles.errorContainer}>
          <Winicon src="outline/user interface/alert-triangle" size={32} color="#FF0000" />
          <Text style={styles.errorText}>Failed to load video</Text>
        </View>
      ) : (
        <TouchableOpacity
          style={StyleSheet.absoluteFill}
          activeOpacity={1}
          onPress={() => setPaused(!paused)}>
          <Video
            ref={videoRef}
            source={{uri: source}}
            style={StyleSheet.absoluteFill}
            paused={paused}
            resizeMode="contain"
            onLoad={handleLoad}
            onProgress={handleProgress}
            onEnd={handleEnd}
            onError={handleError}
          />
          {renderControls()}
        </TouchableOpacity>
      )}

      {/* {isCompleted && !fullscreen && (
        <View style={styles.completeBox}>
          <Text style={styles.completeText}>Video completed</Text>
        </View>
      )} */}
    </View>
  );

  return (
    <View style={[styles.container, fullscreen && styles.fullscreenContainer]}>
      <StatusBar hidden={fullscreen} />
      {renderVideo()}

      {/* Progress bar - chỉ hiển thị khi không ở chế độ fullscreen */}
      {!fullscreen && (
        <>
          <Slider
            style={styles.slider}
            minimumValue={0}
            maximumValue={duration}
            value={currentTime}
            onSlidingStart={onSlidingStart}
            onSlidingComplete={onSlidingComplete}
            minimumTrackTintColor="#FF0000"
            maximumTrackTintColor="#888"
            thumbTintColor="#FFF"
          />

          {/* Hiển thị thời gian nhỏ và nằm cùng hàng với fullscreen */}
          <View style={styles.timeContainer}>
            <Text style={styles.timeText}>
              {formatTime(currentTime)} / {formatTime(duration)}
            </Text>
          </View>
        </>
      )}

      {/* Slider riêng cho chế độ fullscreen - nằm ở dưới cùng */}
      {fullscreen && (
        <View style={styles.fullscreenSliderContainer}>
          <Slider
            style={styles.fullscreenSlider}
            minimumValue={0}
            maximumValue={duration}
            value={currentTime}
            onSlidingStart={onSlidingStart}
            onSlidingComplete={onSlidingComplete}
            minimumTrackTintColor="#FF0000"
            maximumTrackTintColor="#888"
            thumbTintColor="#FFF"
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: '#000',
  },
  videoWrapper: {
    width: width,
    aspectRatio: 16 / 9,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullscreenVideo: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#000',
    zIndex: 999,
    elevation: 10,
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  controls: {
    position: 'absolute',
    bottom: 3,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  leftControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  controlBtn: {
    padding: 8,
  },
  progressBar: {
    height: 3,
    backgroundColor: '#444',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  progress: {
    height: '100%',
    backgroundColor: '#4cd137',
  },
  timeContainer: {
    position: 'absolute',
    bottom: 30,
    right: 70,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  timeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
    marginRight: 10,
  },
  completeBox: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 12,
    backgroundColor: 'rgba(39, 174, 96, 0.8)',
    alignItems: 'center',
  },
  completeText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 10,
  },
  loadingText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 10,
  },
  slider: {
    width: '100%',
    height: 20,
  },
  fullscreenControls: {
    
    bottom: 20,
  },
  fullscreenTimeText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 20,
  },
  fullscreenSlider: {
    width: '100%',
    height: 40,
  },
  fullscreenContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#000',
    zIndex: 1000,
  },
  fullscreenSliderContainer: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
  },
});
