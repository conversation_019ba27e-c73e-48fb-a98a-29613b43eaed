import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Pressable } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { ListFrom } from '../../mock/shopData';

interface ListItemProps {
    setSelecChild: (item: any) => void;
}

const ListItemLable = (props: ListItemProps) => {
    const { setSelecChild } = props

    const [isSelected, setIsSelected] = useState("");
    const [data, setData] = useState<any[]>();
    useEffect(() => {
        if (ListFrom && ListFrom?.length > 0) {
            setData(ListFrom)
        }
        console.log("check-ListFrom", ListFrom)
    }, [])

    const handleSelectLabel = (item: any) => {
        setIsSelected(item.id)
        setSelecChild(item?.name)
    }
    return (
        <FlatList
            data={data}
            style={{ flex: 1 }}
            keyExtractor={(item, i) => `${i} ${item.id}`}
            renderItem={({ item, index }) => (
                <Text
                    style={styles.itemContainer}
                >
                    <Text style={styles.itemText}>{item.name}</Text>
                    <TouchableOpacity style={isSelected == item.id ? [styles.radio, isSelected && styles.radioSelected] : styles.radio} onPress={() => handleSelectLabel(item)} />
                </Text>
            )}
        />

    );
};

const styles = StyleSheet.create({
    itemContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        alignItems: 'center',
        padding: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    itemText: {
        fontSize: 20,
        color: 'black',
        flex: 1,
    },
    radio: {
        width: 30,
        height: 30,
        borderRadius: 50,
        borderWidth: 2,
        borderColor: '#007AFF',
    },
    radioSelected: {
        backgroundColor: '#007AFF',
        padding: 5
    },
});

export default ListItemLable;