import {useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {Chip} from 'react-native-paper';

interface NotificationListChipProps {
  count: number;
  onChange: (type: 'all' | 'affiliate') => void;
}

const NotificationListChip = ({
  count = 0,
  onChange,
}: NotificationListChipProps) => {
  const [choose, setChoose] = useState<'all' | 'affiliate'>('all');
  const isPrimary = (type: string) => {
    return choose === type ? styles.primaryChip : styles.secondaryChip;
  };
  const isPrimaryText = (type: string) => {
    return choose === type ? styles.primaryText : styles.secondaryText;
  };

  const handleChoose = (type: 'all' | 'affiliate') => {
    setChoose(type);
    onChange(type);
  };

  return (
    <View style={styles.containerChip}>
      <Chip
        mode="flat"
        style={[styles.chip, isPrimary('all')]}
        textStyle={isPrimaryText('all')}
        onPress={() => handleChoose('all')}>
        Thông báo chung
      </Chip>

      <Chip
        mode="flat"
        style={[styles.chip, isPrimary('affiliate')]}
        textStyle={isPrimaryText('affiliate')}
        onPress={() => handleChoose('affiliate')}>
        <View
          style={{
            flexDirection: 'row',
          }}>
          <Text style={isPrimaryText('affiliate')}>Affiliate</Text>
          {count > 0 && (
            <View
              style={{
                backgroundColor: 'red',
                borderRadius: 100,
                marginLeft: 10,
              }}>
              <Text
                style={{color: 'white', fontSize: 12, paddingHorizontal: 5}}>
                {count}
              </Text>
            </View>
          )}
        </View>
      </Chip>
    </View>
  );
};

const styles = StyleSheet.create({
  containerChip: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    paddingVertical: 12,
  },
  chip: {
    justifyContent: 'center',
    borderRadius: 20,
    height: 36,
  },
  primaryChip: {
    backgroundColor: '#1C33FF', // Blue background
  },
  primaryText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  secondaryChip: {
    backgroundColor: '#E0E0E0', // Gray background
  },
  secondaryText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default NotificationListChip;
