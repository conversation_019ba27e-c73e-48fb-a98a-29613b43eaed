import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { <PERSON>pp<PERSON>utton, Winicon, showSnackbar, ComponentStatus } from 'wini-mobile-components';
import QRCode from 'react-native-qrcode-svg';
import Clipboard from '@react-native-clipboard/clipboard';
import { ColorThemes } from '../../../assets/skin/colors';
import { TypoSkin } from '../../../assets/skin/typography';

interface Step3QRCodeSetupProps {
  secretKey: string;
  qrCodeValue: string;
  onNext: () => void;
}

const Step3QRCodeSetup: React.FC<Step3QRCodeSetupProps> = ({
  secretKey,
  qrCodeValue,
  onNext,
}) => {
  const copySecretKey = () => {
    Clipboard.setString(secretKey);
    showSnackbar({
      message: 'Đã sao chép mã bí mật',
      status: ComponentStatus.SUCCSESS,
    });
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {/* Description */}
        <View style={styles.descriptionContainer}>
          <Text style={styles.descriptionText}>
            Quét mã QR này bằng ứng dụng xác thực (authenticator) của bạn.
          </Text>
        </View>

        {/* QR Code */}
        <View style={styles.qrContainer}>
          <View style={styles.qrCodeWrapper}>
            <QRCode
              value={qrCodeValue}
              size={200}
              backgroundColor="white"
              color="black"
            />
          </View>
        </View>

        {/* Alternative method */}
        <View style={styles.alternativeContainer}>
          <Text style={styles.alternativeText}>
            Nếu không quét được. Sao chép mã bên dưới.
          </Text>
          
          <View style={styles.secretKeyContainer}>
            <View style={styles.secretKeyInputContainer}>
              <Text style={styles.secretKeyText} numberOfLines={1}>
                {secretKey}
              </Text>
              <TouchableOpacity
                style={styles.copyButton}
                onPress={copySecretKey}
              >
                <Winicon
                  src="outline/user interface/copy"
                  size={20}
                  color={ColorThemes.light.secondary1_main_color}
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>

      {/* Bottom Button */}
      <View style={styles.buttonContainer}>
        <AppButton
          title="Tiếp theo"
          onPress={onNext}
          backgroundColor={ColorThemes.light.primary_main_color}
          titleColor={ColorThemes.light.white}
          containerStyle={styles.button}
          textStyle={styles.buttonText}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
  },
  content: {
    flex: 1,
    paddingTop: 20,
    alignItems: 'center',
  },
  descriptionContainer: {
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  descriptionText: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
    textAlign: 'center',
    lineHeight: 20,
    fontWeight: '500',
  },
  qrContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  qrCodeWrapper: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  alternativeContainer: {
    width: '100%',
    paddingHorizontal: 20,
  },
  alternativeText: {
    ...TypoSkin.regular1,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
    marginBottom: 16,
  },
  secretKeyContainer: {
    marginBottom: 20,
  },
  secretKeyInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderRadius: 8,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    paddingHorizontal: 16,
    height: 48,
  },
  secretKeyText: {
    flex: 1,
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
    fontFamily: 'monospace',
  },
  copyButton: {
    padding: 4,
    marginLeft: 8,
  },
  buttonContainer: {
    paddingBottom: 20,
  },
  button: {
    height: 48,
    borderRadius: 24,
  },
  buttonText: {
    ...TypoSkin.buttonText1,
    fontWeight: '600',
  },
});

export default Step3QRCodeSetup;
