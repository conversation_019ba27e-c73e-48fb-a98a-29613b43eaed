import {differenceInCalendarDays, differenceInMinutes} from 'date-fns';
import {validate} from 'validate.js';
import {UseFormReturn} from 'react-hook-form';
import {CSSProperties} from 'react';
import {Text, View} from 'react-native';
import {BaseDA} from '../../../base/BaseDA';
import {ComponentType, FEDataType, ValidateType} from './da';
import {
  CheckboxForm,
  DropdownForm,
  FRadioForm,
  SWitchForm,
  TextFieldForm,
} from './component-form';
import {Checkbox, WSwitch} from 'wini-mobile-components';
import {Ultis} from '../../../utils/Utils';

export function RenderComponentByType({
  fieldItem,
  methods,
  style = {},
  labelStyle = {},
  label,
}: {
  fieldItem: any;
  methods: UseFormReturn;
  style?: CSSProperties;
  labelStyle?: CSSProperties;
  label?: string;
}) {
  switch (fieldItem.Form.ComponentType) {
    case ComponentType.textField:
      return fieldItem.DataType === FEDataType.PASSWORD ? (
        <TextFieldForm
          errors={methods.formState.errors}
          control={methods.control}
          name={fieldItem.Name}
          secureTextEntry
          label={label ?? fieldItem.Form.Label}
          required={fieldItem.Form.Required}
          disabled={fieldItem.Form.Disabled}
          textFieldStyle={{paddingHorizontal: 16}}
          placeholder={fieldItem.Form.Placeholder}
          maxLength={fieldItem.Form.Max}
        />
      ) : (
        <TextFieldForm
          errors={methods.formState.errors}
          control={methods.control}
          name={fieldItem.Name}
          required={fieldItem.Form.Required}
          disabled={fieldItem.Form.Disabled}
          label={label ?? fieldItem.Form.Label}
          placeholder={fieldItem.Form.Placeholder}
          textFieldStyle={{paddingHorizontal: 16}}
          maxLength={fieldItem.Form.Max}
          type={
            fieldItem.DataType === FEDataType.NUMBER
              ? 'number-pad'
              : fieldItem.DataType === FEDataType.MONEY
              ? 'money'
              : 'default'
          }
        />
      );
    case ComponentType.textArea:
      return (
        <TextFieldForm
          errors={methods.formState.errors}
          control={methods.control}
          required={fieldItem.Form.Required}
          disabled={fieldItem.Form.Disabled}
          label={label ?? fieldItem.Form.Label}
          placeholder={fieldItem.Form.Placeholder}
          name={fieldItem.Name}
          textFieldStyle={{
            height: 203,
            paddingHorizontal: 16,
            paddingTop: 16,
            paddingBottom: 16,
            justifyContent: 'flex-start',
            backgroundColor: 'transparent',
          }}
          textStyle={{textAlignVertical: 'top'}}
          numberOfLines={fieldItem.Form.Max}
          multiline={true}
          type={
            fieldItem.DataType === FEDataType.NUMBER
              ? 'number-pad'
              : fieldItem.DataType === FEDataType.MONEY
              ? 'money'
              : 'default'
          }
        />
      );
    case ComponentType.switch:
      return (
        <SWitchForm
          value={methods.getValues(fieldItem.Name)}
          disabled={fieldItem.Form.Disabled}
          onChange={value => {
            methods.setValue(fieldItem.Name, value);
          }}
          control={methods.control}
          label={label ?? fieldItem.Form.Label}
          name={fieldItem.Name}
        />
      );
    // case ComponentType.rate:
    //   return (
    //     <div
    //       className={className ?? 'row'}
    //       style={{width: '100%', justifyContent: 'space-between', ...style}}>
    //       {fieldItem.Form.Title ? (
    //         <Text className="label-3" style={labelStyle}>
    //           {fieldItem.Form.Title}
    //         </Text>
    //       ) : undefined}
    //       <RateForm
    //         methods={methods}
    //         label={label ?? fieldItem.Form.Label}
    //         name={fieldItem.Name}
    //         size={'2.4rem'}
    //       />
    //     </div>
    //   );
    case ComponentType.checkbox:
      return (
        <CheckboxForm
          control={methods.control}
          value={methods.getValues(fieldItem.Name)}
          onChange={value => {
            methods.setValue(fieldItem.Name, value);
          }}
          name={fieldItem.Name}
          label={label ?? fieldItem.Form.Label}
        />
      );
    case ComponentType.radio:
      return (
        <FRadioForm
          control={methods.control}
          value={methods.getValues(fieldItem.Name)}
          onChange={value => {
            methods.setValue(fieldItem.Name, value);
          }}
          name={fieldItem.Name}
          //   options={fieldItem.Form.Options}
          label={label ?? fieldItem.Form.Label}
          //   required={fieldItem.Form.Required}
        />
      );
    case ComponentType.select1:
      return (
        <DropdownForm
          control={methods.control}
          errors={methods.formState.errors}
          required={fieldItem.Form.Required}
          disabled={fieldItem.Form.Disabled}
          label={label ?? fieldItem.Form.Label}
          placeholder={fieldItem.Form.Placeholder}
          name={fieldItem.Name}
          options={fieldItem.Form.Options ?? []}
        />
      );
    // case ComponentType.selectMultiple:
    //   return (
    //     <SelectMultipleForm
    //       methods={methods}
    //       required={fieldItem.Form.Required}
    //       disabled={fieldItem.Form.Disabled}
    //       label={label ?? fieldItem.Form.Label}
    //       placeholder={fieldItem.Form.Placeholder}
    //       name={fieldItem.Name}
    //       options={fieldItem.Form.Options ?? []}
    //       className={className}
    //       style={style}
    //     />
    //   );
    // case ComponentType.checkbox:
    //   return fieldItem.Form?.Options?.length ? (
    //     <GroupCheckboxForm
    //       methods={methods}
    //       disabled={fieldItem.Form.Disabled}
    //       label={label ?? fieldItem.Form.Label}
    //       name={fieldItem.Name}
    //       className={className}
    //       style={style}
    //       dataType={'string'}
    //       options={fieldItem.Form.Options}
    //     />
    //   ) : (
    //     <CheckboxForm
    //       methods={methods}
    //       disabled={fieldItem.Form.Disabled}
    //       label={label ?? fieldItem.Form.Label}
    //       name={fieldItem.Name}
    //       style={style}
    //     />
    //   );
    // case ComponentType.upload:
    //   return (
    //     <ImportFileForm
    //       methods={methods}
    //       required={fieldItem.Form.Required}
    //       label={label ?? fieldItem.Form.Label}
    //       name={fieldItem.Name}
    //       allowType={fieldItem.Form.AcceptFiles?.split(',')}
    //       subTitle={fieldItem.Form.AcceptFiles}
    //       maxSize={fieldItem.Form.Max}
    //       multiple={fieldItem.Form.Multiple}
    //       className={className}
    //       style={style}
    //       disabled={fieldItem.Form.Disabled}
    //     />
    //   );
    case ComponentType.datePicker:
    // case ComponentType.dateTimePicker:
    //   return (
    //     <DateTimePickerForm
    //       methods={methods}
    //       required={fieldItem.Form.Required}
    //       disabled={fieldItem.Form.Disabled}
    //       label={label ?? fieldItem.Form.Label}
    //       placeholder={fieldItem.Form.Placeholder}
    //       name={fieldItem.Name}
    //       type={
    //         fieldItem.Form.ComponentType === ComponentType.datePicker
    //           ? 'date'
    //           : 'datetime'
    //       }
    //       className={className}
    //       style={style}
    //     />
    //   );
    // case ComponentType.range:
    //   const splitName = fieldItem.Name.split(',');
    //   const splitPlaceholder = fieldItem.Form.Placeholder?.split(',');
    //   return (
    //     <RangeForm
    //       methods={methods}
    //       className={className}
    //       type={
    //         fieldItem.DataType === FEDataType.DATE
    //           ? 'daterange'
    //           : fieldItem.DataType === FEDataType.DATETIME
    //           ? 'datetimerange'
    //           : 'number'
    //       }
    //       name={splitName[0].trim()}
    //       endName={splitName[1].trim()}
    //       placeholder={fieldItem.Form.Placeholder}
    //       placeholderStart={splitPlaceholder?.[0]?.trim()}
    //       placeholderEnd={splitPlaceholder?.[1]?.trim()}
    //       label={label ?? fieldItem.Form.Label}
    //       disabled={fieldItem.Form.Disabled}
    //       style={style}
    //     />
    //   );
    default:
      return (
        <View>
          <Text>{fieldItem.Form.ComponentType}</Text>
        </View>
      );
  }
}

// {Name:, Validate}
export async function validateForm({
  list = [],
  formdata,
}: {
  list?: Array<{[p: string]: any}>;
  formdata?: {[p: string]: any};
}) {
  const val = validate as any;
  val.validators.customDate = customValidateDateTime;
  val.validators.myAsyncValidator = myAsyncValidator;
  val.options = {fullMessages: false};
  const myValidators = validateByType({list: list});
  let res = validate(formdata, myValidators.validator);
  if (!res && Object.keys(myValidators.asyncValidator).length) {
    try {
      res = await val.async(formdata, myValidators.asyncValidator);
    } catch (error) {
      res = error;
    }
  }
  return res;
}

function validateByType({list = []}: {list?: Array<{[p: string]: any}>}) {
  let validator: {[p: string]: any} = {};
  let asyncValidator: {[p: string]: any} = {};
  list.forEach(e => {
    let eValidateConfig: {[p: string]: any} = {};
    e.Validate?.forEach((el: any) => {
      switch (el.type) {
        case ValidateType.email:
          eValidateConfig.email = {message: el.message ?? 'Invalid email'};
          break;
        case ValidateType.minLength:
          eValidateConfig.length = {
            ...(eValidateConfig.length ?? {}),
            minimum: el.value,
            tooShort: el.message ?? `At least ${el.value} characters`,
          };
          break;
        case ValidateType.maxLength:
          eValidateConfig.length = {
            ...(eValidateConfig.length ?? {}),
            maximum: el.value,
            tooLong: el.message ?? `At most ${el.value} characters`,
          };
          break;
        case ValidateType.number:
          eValidateConfig.format = {
            pattern: '[0-9]+',
            flags: 'i',
            message: el.message ?? `Only number`,
          };
          break;
        case ValidateType.phone:
          eValidateConfig.format = {
            pattern: '([+]{0,1})(84|0[3|5|7|8|9])+([0-9]{8})',
            flags: 'g',
            message: el.message ?? `Invalid phone number`,
          };
          break;
        // case ValidateType.date:
        //     eValidateConfig.customDate = { dateOnly: true, message: el.message ?? `Không đúng định dạng dd/mm/yyyy` }
        //     break;
        // case ValidateType.dateTime:
        //     eValidateConfig.customDate = { message: el.message ?? `Không đúng định dạng dd/mm/yyyy hh:mm` }
        //     break;
        // case ValidateType.earliestDate:
        //     eValidateConfig.customDate = { dateOnly: true, earliest: el.value, tooEarly: el.message ?? `Không được trước ${Ultis.datetoString(new Date(el.value))}` }
        //     break;
        // case ValidateType.latestDate:
        //     eValidateConfig.customDate = { dateOnly: true, latest: el.value, tooLate: el.message ?? `Không được sau ${Ultis.datetoString(new Date(el.value))}` }
        //     break;
        // case ValidateType.earliestTime:
        //     eValidateConfig.customDate = { earliest: el.value, tooEarly: el.message ?? `Không được trước ${Ultis.datetoString(new Date(el.value))}` }
        //     break;
        // case ValidateType.latestTime:
        //     eValidateConfig.customDate = { latest: el.value, tooLate: el.message ?? `Không được sau ${Ultis.datetoString(new Date(el.value))}` }
        //     break;
        case ValidateType.greaterThan:
          eValidateConfig.numericality = {
            ...(eValidateConfig.numericality ?? {}),
            greaterThan: el.value,
            notGreaterThan:
              el.message ?? `Value must be greater than ${el.value}`,
          };
          break;
        case ValidateType.greaterThanOrEqualTo:
          eValidateConfig.numericality = {
            ...(eValidateConfig.numericality ?? {}),
            greaterThanOrEqualTo: el.value,
            notGreaterThan:
              el.message ??
              `Value must be greater than or equal to ${el.value}`,
          };
          break;
        case ValidateType.lessThan:
          eValidateConfig.numericality = {
            ...(eValidateConfig.numericality ?? {}),
            lessThan: el.value,
            notLessThan: el.message ?? `Value must be less than ${el.value}`,
          };
          break;
        case ValidateType.lessThanOrEqualTo:
          eValidateConfig.numericality = {
            ...(eValidateConfig.numericality ?? {}),
            lessThanOrEqualTo: el.value,
            notLessThanOrEqualTo:
              el.message ?? `Value must be less than or equal to ${el.value}`,
          };
          break;
        // case ValidateType.async:
        //     asyncValidator[e.Name] = { myAsyncValidator: { url: el.value } }
        //     break;
        default:
          break;
      }
    });
    validator[e.Name] = eValidateConfig;
  });
  return {
    validator: validator,
    asyncValidator: asyncValidator,
  };
}

function customValidateDateTime(value: any, options: {[p: string]: any}) {
  try {
    const parseValue: any =
      typeof value === 'string'
        ? Ultis.stringToDate(
            value,
            options.dateOnly ? 'dd/mm/yyyy' : 'dd/mm/yyyy hh:mm',
          )
        : new Date(value);
    if (options.earliest) {
      try {
        var _earliest: any =
          typeof options.earliest === 'string'
            ? Ultis.stringToDate(
                value,
                options.dateOnly ? 'dd/mm/yyyy' : 'dd/mm/yyyy hh:mm',
              )
            : new Date(options.earliest);
      } catch (error) {
        console.log(error);
      }
    }
    if (options.latest) {
      try {
        var _latest: any =
          typeof options.latest === 'string'
            ? Ultis.stringToDate(
                value,
                options.dateOnly ? 'dd/mm/yyyy' : 'dd/mm/yyyy hh:mm',
              )
            : new Date(options.latest);
      } catch (error) {
        console.log(error);
      }
    }
    if (isNaN(parseValue)) {
      return options.message;
    } else if (_earliest) {
      if (
        options.dateOnly &&
        differenceInCalendarDays(parseValue, _earliest) < 0
      ) {
        return options.tooEarly;
      } else if (
        !options.dateOnly &&
        differenceInMinutes(parseValue, _earliest) < 0
      ) {
        return options.tooEarly;
      }
    } else if (_latest) {
      if (
        options.dateOnly &&
        differenceInCalendarDays(parseValue, _latest) < 0
      ) {
        return options.tooLate;
      } else if (
        !options.dateOnly &&
        differenceInMinutes(parseValue, _latest) < 0
      ) {
        return options.tooLate;
      }
    }
  } catch (error) {
    return options.message;
  }
  return;
}

async function myAsyncValidator(value: any, options: any) {
  console.log('????????: ', value, ' -----------: ', options);
  if (options.url) {
    const res = await BaseDA.post(options.url, {
      body: {value: value},
    });
    if (res) {
      if (res.code !== 200) return res.message;
    }
  }
  return undefined;
}

export const regexGetVariableByThis = /\${this\.(\w+)}/g;
