import React, {use, useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
} from 'react-native';
import {FlatList} from 'react-native-gesture-handler';
import {ListTile, Winicon} from 'wini-mobile-components';
import {useNavigation} from '@react-navigation/native';
import ListItemCard from '../card/CartProductCreate';
import {DataController} from '../../../base/baseController';
import {ListItemProps} from '../../dto/dto';

const ListItem = (props: ListItemProps) => {
  const {
    dataProduct,
    setSelecChildID,
    setSelecChildName,
    selectItemChild,
    setSelectItemChild,
  } = props;
  const CategoryController = new DataController('Category');
  const [isSelected, setIsSelected] = useState('');
  const [data, setData] = useState<any[]>();
  useEffect(() => {
    if (dataProduct && dataProduct?.length > 0) {
      setData(dataProduct);
    }
  }, [dataProduct]);

  const handleSelect = (item: any) => {
    console.log('check-item-handleSelect', item);
    setSelecChildID(item?.Id);
    setSelecChildName(item?.Name);
    if (item?.Id !== isSelected) {
      setIsSelected(item.Id);
    } else {
      setIsSelected('');
    }
  };

  return (
    <FlatList
      data={selectItemChild ? [selectItemChild] : data}
      style={{flex: 1, marginBottom: 50}}
      keyExtractor={(item, i) => `${i} ${item.id}`}
      renderItem={({item, index}) =>
        ListItemCard(
          {item, index},
          isSelected,
          handleSelect,
          selectItemChild,
          setSelectItemChild,
        )
      }
    />
  );
};

export default ListItem;
