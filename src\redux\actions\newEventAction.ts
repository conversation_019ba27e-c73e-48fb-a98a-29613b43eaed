import {createAsyncThunk} from '@reduxjs/toolkit';
import {showSnackbar, ComponentStatus} from 'wini-mobile-components';
import {DataController} from '../../base/baseController';
import {RootState} from '../store/store';
import {NewsEvent} from '../models/newsEvent';
import {BaseDA} from '../../base/BaseDA';
import ConfigAPI from '../../Config/ConfigAPI';

const fetchNewsEvents = createAsyncThunk<
  NewsEvent[],
  {page?: number; status?: number} | undefined,
  {state: RootState}
>('newsEvent/fetchData', async (config, thunkAPI) => {
  const controller = new DataController('NewsEvent');
  // const cusId = thunkAPI.getState().customer.data?.Id; // Example of getting state
  try {
    const res = await controller.aggregateList({
      page: config?.page ?? 1,
      size: 1000,
      //   searchRaw: `@CustomerId:{${cusId}}`,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res.code === 200) {
      const listData = await getImageEdit({items: res.data});
      return listData;
    } else {
      const errorMessage = res.message || 'Failed to fetch events';
      showSnackbar({
        message: errorMessage,
        status: ComponentStatus.ERROR,
      });
      return thunkAPI.rejectWithValue(errorMessage);
    }
  } catch (err: any) {
    const errorMessage = err.message || 'An unknown error occurred';
    showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

const getImageEdit = async ({items}: {items: any}) => {
  const url = ConfigAPI.url.split('/api/')[0];

  for (const item of items) {
    if (item.Img) {
      const imageAvata = await BaseDA.getFilesInfor([item.Img]);
      item.Img = `${url}${imageAvata.data[0].Url}`;
    }
  }

  return items;
};

export {fetchNewsEvents};
