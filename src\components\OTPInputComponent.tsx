import React, { useState, useRef, useEffect } from 'react';
import { View, TextInput, StyleSheet, TouchableOpacity } from 'react-native';
import { Text } from 'react-native-paper';
import { ColorThemes } from '../assets/skin/colors';
import { <PERSON><PERSON><PERSON>kin } from '../assets/skin/typography';

interface OTPInputComponentProps {
  length?: number;
  onComplete: (otp: string) => void;
  phoneNumber?: string;
  onResend?: () => void;
}

const OTPInputComponent: React.FC<OTPInputComponentProps> = ({
  length = 6,
  onComplete,
  phoneNumber,
  onResend,
}) => {
  const [otp, setOtp] = useState<string[]>(Array(length).fill(''));
  const [timer, setTimer] = useState(60);
  const inputsRef = useRef<(TextInput | null)[]>([]);

  useEffect(() => {
    if (timer > 0) {
      const interval = setTimeout(() => setTimer(timer - 1), 1000);
      return () => clearTimeout(interval);
    }
  }, [timer]);

  const handleChange = (text: string, index: number) => {
    if (text.length > 1) return;
    
    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);

    // Auto focus next input
    if (text && index < length - 1) {
      inputsRef.current[index + 1]?.focus();
    }

    // Check if OTP is complete
    if (newOtp.every(digit => digit !== '') && newOtp.join('').length === length) {
      onComplete(newOtp.join(''));
    }
  };

  const handleBackspace = (index: number) => {
    const newOtp = [...otp];
    if (newOtp[index] === '' && index > 0) {
      inputsRef.current[index - 1]?.focus();
      newOtp[index - 1] = '';
    } else {
      newOtp[index] = '';
    }
    setOtp(newOtp);
  };

  const handleResend = () => {
    if (timer === 0 && onResend) {
      onResend();
      setTimer(60);
      setOtp(Array(length).fill(''));
      inputsRef.current[0]?.focus();
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Nhập mã xác nhận</Text>

      {phoneNumber && (
        <Text style={styles.subtitle}>
          Chúng tôi đã gửi mã xác nhận đến {phoneNumber}. Vui lòng kiểm tra tin nhắn!
        </Text>
      )}

      <View style={styles.otpContainer}>
        {Array.from({ length }, (_, index) => (
          <TextInput
            key={index}
            ref={el => {
              inputsRef.current[index] = el;
            }}
            style={[
              styles.otpInput,
              otp[index] ? styles.otpInputFilled : null,
            ]}
            value={otp[index]}
            onChangeText={text => handleChange(text, index)}
            onKeyPress={({ nativeEvent }) => {
              if (nativeEvent.key === 'Backspace') {
                handleBackspace(index);
              }
            }}
            keyboardType="numeric"
            maxLength={1}
            textContentType="oneTimeCode"
            autoFocus={index === 0}
          />
        ))}
      </View>

      {timer > 0 ? (
        <Text style={styles.timerText}>
          Vui lòng đợi {timer} giây trước khi gửi lại mã
        </Text>
      ) : (
        <TouchableOpacity onPress={handleResend}>
          <Text style={styles.resendText}>Gửi lại mã</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 32,
    alignItems: 'center',
  },
  title: {
    ...TypoSkin.heading5,
    color: ColorThemes.light.primary_main_color,
    textAlign: 'center',
    marginBottom: 16,
  },
  subtitle: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 20,
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 12,
    marginBottom: 32,
  },
  otpInput: {
    width: 40,
    height: 40,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_lighter_border_color,
    backgroundColor: ColorThemes.light.white,
    textAlign: 'center',
    fontSize: 18,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_title_color,
  },
  otpInputFilled: {
    borderColor: ColorThemes.light.primary_main_color,
    backgroundColor: ColorThemes.light.primary_background,
  },
  timerText: {
    ...TypoSkin.regular3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
  },
  resendText: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.primary_main_color,
    textAlign: 'center',
    textDecorationLine: 'underline',
  },
});

export default OTPInputComponent;
