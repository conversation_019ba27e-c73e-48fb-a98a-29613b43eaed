// Đ<PERSON><PERSON> nghĩa các types cho giỏ hàng

// Đ<PERSON>nh nghĩa cấu trúc của một sản phẩm trong giỏ hàng
export interface CartItem {
  id: string;
  ProductId: string;
  ShopId: string;
  ShopName: string;
  ShopAvatar: string;
  Discount: number;
  Name: string;
  Img: string;
  Price: number;
  Quantity: number;
  selected: boolean;
  // Các thuộc tính khác của sản phẩm nếu cần
}

// Định nghĩa cấu trúc của state giỏ hàng
export interface CartState {
  items: CartItem[];
  loading: boolean;
  error: string | null;
}

// Định nghĩa các action types
export enum CartActionTypes {
  ADD_TO_CART = 'ADD_TO_CART',
  REMOVE_FROM_CART = 'REMOVE_FROM_CART',
  UPDATE_QUANTITY = 'UPDATE_QUANTITY',
  TOGGLE_SELECT_ITEM = 'TOGGLE_SELECT_ITEM',
  SELECT_STORE_ITEMS = 'SELECT_STORE_ITEMS',
  CLEAR_CART = 'CLEAR_CART',
  LOAD_CART = 'LOAD_CART',
  LOAD_CART_SUCCESS = 'LOAD_CART_SUCCESS',
  LOAD_CART_FAILURE = 'LOAD_CART_FAILURE',
}

// Định nghĩa các action interfaces
export interface AddToCartAction {
  type: CartActionTypes.ADD_TO_CART;
  payload: CartItem;
}

export interface RemoveFromCartAction {
  type: CartActionTypes.REMOVE_FROM_CART;
  payload: string; // id của sản phẩm cần xóa
}

export interface UpdateQuantityAction {
  type: CartActionTypes.UPDATE_QUANTITY;
  payload: {
    id: string;
    quantity: number;
  };
}

export interface ToggleSelectItemAction {
  type: CartActionTypes.TOGGLE_SELECT_ITEM;
  payload: string; // id của sản phẩm cần toggle
}

export interface SelectStoreItemsAction {
  type: CartActionTypes.SELECT_STORE_ITEMS;
  payload: {
    storeId: string;
    selected: boolean;
  };
}

export interface ClearCartAction {
  type: CartActionTypes.CLEAR_CART;
}

export interface LoadCartAction {
  type: CartActionTypes.LOAD_CART;
}

export interface LoadCartSuccessAction {
  type: CartActionTypes.LOAD_CART_SUCCESS;
  payload: CartItem[];
}

export interface LoadCartFailureAction {
  type: CartActionTypes.LOAD_CART_FAILURE;
  payload: string; // error message
}

// Union type cho tất cả các actions
export type CartAction =
  | AddToCartAction
  | RemoveFromCartAction
  | UpdateQuantityAction
  | ToggleSelectItemAction
  | SelectStoreItemsAction
  | ClearCartAction
  | LoadCartAction
  | LoadCartSuccessAction
  | LoadCartFailureAction;
