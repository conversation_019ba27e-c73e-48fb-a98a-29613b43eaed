import React, {use, useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Pressable,
  Image,
  ScrollView,
} from 'react-native';
import {ListTile, Winicon} from 'wini-mobile-components';
import ConfigAPI from '../../../Config/ConfigAPI';
import {ColorThemes} from '../../../assets/skin/colors';
import {Radio, RadioAction} from '../../Field/Radio';

const ListItemCard = (
  {item, index}: {item: any; index: number},
  isSelected: string,
  handleSelect: any,
  selectItemChild: any,
  setSelectItemChild: any,
) => {
  {
    return (
      <Pressable style={styles.itemContainer}>
        <View style={styles.itemText}>
          <Image
            source={{
              uri: ConfigAPI.urlImg + item.Img,
            }}
            style={{width: 32, height: 32, borderRadius: 100}}
          />
          <Text
            style={
              selectItemChild
                ? {marginLeft: 10, fontSize: 20}
                : {marginLeft: 10, fontSize: 20, fontFamily: 'roboto'}
            }>
            {item.Name}
          </Text>
        </View>
        {selectItemChild && selectItemChild?.Id ? (
          <TouchableOpacity onPress={() => handleSelect(item)}>
            {isSelected == item.Id ? <RadioAction /> : <Radio />}
          </TouchableOpacity>
        ) : (
          <TouchableOpacity onPress={() => setSelectItemChild(item)}>
            <Winicon
              src="color/arrows/arrow-right"
              size={20}
              color={ColorThemes.light.neutral_text_title_color}
            />
          </TouchableOpacity>
        )}
      </Pressable>
    );
  }
};

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingLeft: 10,
    paddingRight: 20,
    paddingTop: 10,
    paddingBottom: 10,
  },
  itemText: {
    fontSize: 20,
    color: 'black',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default ListItemCard;
