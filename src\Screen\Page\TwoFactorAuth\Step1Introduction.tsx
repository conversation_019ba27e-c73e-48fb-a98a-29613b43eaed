import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { AppButton } from 'wini-mobile-components';
import { ColorThemes } from '../../../assets/skin/colors';
import { TypoSkin } from '../../../assets/skin/typography';

interface Step1IntroductionProps {
  onNext: () => void;
}

const Step1Introduction: React.FC<Step1IntroductionProps> = ({ onNext }) => {
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {/* Illustration */}
        <View style={styles.illustrationContainer}>
          <Image
            source={require('../../../assets/images/phone.png')}
            style={styles.illustration}
            resizeMode="contain"
          />
        </View>

        {/* Description */}
        <View style={styles.descriptionContainer}>
          <Text style={styles.descriptionText}>
            Bạn chưa bật xác thực 2 lớp MFA
          </Text>
        </View>
      </View>

      {/* Bottom Button */}
      <View style={styles.buttonContainer}>
        <AppButton
          title="Bật xác thực 2 lớp"
          onPress={onNext}
          backgroundColor={ColorThemes.light.primary_main_color}
          containerStyle={styles.button}
          textStyle={styles.buttonText}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  illustrationContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  illustration: {
    width: 280,
    height: 200,
  },
  descriptionContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  descriptionText: {
    ...TypoSkin.regular3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
    lineHeight: 24,
  },
  buttonContainer: {
    paddingBottom: 20,
  },
  button: {
    height: 48,
    borderRadius: 24,
    marginHorizontal: 20,
  },
  buttonText: {
    ...TypoSkin.buttonText1,
    fontWeight: '600',
  },
});

export default Step1Introduction;
